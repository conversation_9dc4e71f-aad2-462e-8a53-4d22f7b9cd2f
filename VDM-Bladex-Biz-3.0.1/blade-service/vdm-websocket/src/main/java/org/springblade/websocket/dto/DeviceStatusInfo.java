package org.springblade.websocket.dto;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;

/**
 * 设备状态信息DTO
 * 包含状态值和状态来源信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DeviceStatusInfo {

	// 状态字段常量
	public static final String FIELD_ONLINE = "online";
	public static final String FIELD_FUSION_STATE = "fusionState";
	public static final String FIELD_ACC = "acc";
	public static final List<String> STATUS_FIELDS = Arrays.asList(FIELD_ONLINE, FIELD_FUSION_STATE, FIELD_ACC);

    /**
     * 设备在线状态
     * 0-离线，1-在线
     */
    private Integer online;

    /**
     * 设备运动状态（融合状态）
     * 1-静止，2-运动
     */
    private Integer fusionState;

    /**
     * 设备ACC状态
     * 0-关闭，1-开启
     */
    private Integer acc;

    /**
     * 状态来源类型
     * TARGET - 从监控对象节点获取
     * DEVICE - 从设备节点获取
     */
    private StatusSource source;

    /**
     * 状态来源的Redis键
     */
    private String sourceKey;

    /**
     * 状态来源枚举
     */
    public enum StatusSource {
        /**
         * 从监控对象节点获取（单设备组情况）
         */
        TARGET,

        /**
         * 从设备节点获取（多设备组情况）
         */
        DEVICE
    }

    /**
     * 创建从监控对象获取的状态信息
     */
    public static DeviceStatusInfo fromTarget(Integer status, Integer fusionState, Integer accStatus, String targetKey) {
        return new DeviceStatusInfo(status, fusionState, accStatus, StatusSource.TARGET, targetKey);
    }

    /**
     * 创建从设备节点获取的状态信息
     */
    public static DeviceStatusInfo fromDevice(Integer status, Integer fusionState, Integer accStatus, String deviceKey) {
        return new DeviceStatusInfo(status, fusionState, accStatus, StatusSource.DEVICE, deviceKey);
    }

    /**
     * 判断是否从监控对象获取
     */
    public boolean isFromTarget() {
        return StatusSource.TARGET.equals(source);
    }

    /**
     * 判断是否从设备节点获取
     */
    public boolean isFromDevice() {
        return StatusSource.DEVICE.equals(source);
    }
}
