package com.xh.vdm.bi.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xh.vdm.bi.constant.BaseInfoConstants;
import com.xh.vdm.bi.enums.*;
import com.xh.vdm.bi.mapper.MonitDeviceMapper;
import com.xh.vdm.bi.service.*;
import com.xh.vdm.bi.service.bdCheck.IBdcTerminalService;
import com.xh.vdm.bi.utils.DeviceNumUtils;
import com.xh.vdm.bi.utils.MapperUtils;
import com.xh.vdm.bi.utils.SnowflakeIdWorker;
import com.xh.vdm.bi.utils.TargetSnowflakeIdWorker;
import com.xh.vdm.bi.vo.request.DeviceNoBindRequest;
import com.xh.vdm.bi.vo.request.FacilityTerminalRequest;
import com.xh.vdm.bi.vo.request.MonitDeviceRequest;
import com.xh.vdm.bi.vo.request.TerminalIotRequest;
import com.xh.vdm.bi.vo.response.DeviceInfo;
import com.xh.vdm.bi.vo.response.FacilityNoBingResponse;
import com.xh.vdm.bi.vo.response.MonitDeviceResponse;
import com.xh.vdm.biapi.entity.*;
import com.xh.vdm.interManager.feign.IMessageClient;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.Constants;
import org.springblade.common.enums.Operation;
import org.springblade.common.utils.CETokenUtil;
import org.springblade.common.utils.DeptProcessingUtil;
import org.springblade.common.utils.MybatisPlusWrapperUitls;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.entity.AuthInfo;
import org.springblade.entity.DataAuthCE;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 北斗监测管理
 */
@Service
@Slf4j
public class MonitDeviceServiceImpl extends ServiceImpl<MonitDeviceMapper, BdmMonitDevice> implements MonitDeviceService {
	@Resource
	private MonitDeviceMapper monitDeviceMapper;
	@Resource
	private IotCardService iotCardService;
	@Resource
	private RedisTemplate redisTemplate;
	@Resource
	private CETokenUtil ceTokenUtil;
	@Autowired
	private DeviceNumUtils deviceNumUtils;
	@Resource
	private IBdcTerminalService terminalService;
	@Resource
	private BdmDeviceCodeService deviceCodeService;
	@Resource
	private KafkaTemplate<String, String> kafkaTemplate;
	@Resource
	private BdmDeviceLedgerService deviceLedgerService;
	@Resource
	private IMessageClient messageClient;
	@Value("${current.schema}")
	String schema;
	@Resource
	private IBdmVirtualTargetService bdmVirtualTargetService;
	@Resource
	private IBdmAbstractDeviceService bdmAbstractDeviceService;
	@Resource
	private IBdmAbstractTargetService bdmAbstractTargetService;
	@Resource
	private DeptProcessingUtil deptProcessingUtil;

	/**
	 * 分页查询
	 *
	 * @param monitDeviceRequest 筛选条件
	 * @param ceDataAuth
	 * @return 查询结果
	 */
	@Override
	public IPage<MonitDeviceResponse> queryByPage(MonitDeviceRequest monitDeviceRequest, DataAuthCE ceDataAuth) {
		Page page = new Page();
		page.setCurrent(monitDeviceRequest.getCurrent());
		page.setSize(monitDeviceRequest.getSize());
		AuthInfo response = deptProcessingUtil.handle(ceDataAuth);
		return this.monitDeviceMapper.queryAll(page, monitDeviceRequest, response.getAccount(), response.getOrgList(), schema);
	}

	/**
	 * 通过ID查询单条数据
	 *
	 * @param id 主键
	 * @return 实例对象
	 */
	@Override
	public MonitDeviceResponse detail(Long id) {
		return this.monitDeviceMapper.detail(id);
	}

	/**
	 * 新增数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public BdmMonitDevice insert(MonitDeviceRequest request) {
		BdmMonitDevice bdmMonitDevice = new BdmMonitDevice();
		QueryWrapper<BdmMonitDevice> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("unique_id", request.getUniqueId());
		BdmMonitDevice monit = baseMapper.selectOne(queryWrapper);
		TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(BaseInfoConstants.VIRTUAL_TARGET_TYPE, BaseInfoConstants.ZERO, BaseInfoConstants.ZERO);
		if (monit != null) {
			request.setId(monit.getId());
			if (request.getSpecificity().equals(BdmTypeEnum.OLD.getValue())) {
				R<String> r1 = deviceNumUtils.deviceNumProducer(ProductTypeEnum.O.name());
				if (r1.isSuccess()) {
					request.setDeviceNum(r1.getData());
				}
			}
			if (BdmTypeEnum.SPECIFICITY.getValue().equals(request.getSpecificity())) {
				R<String> r1 = deviceNumUtils.deviceNumProducer(ProductTypeEnum.S.name());
				if (r1.isSuccess()) {
					request.setDeviceNum(r1.getData());
				}
			}
			if (BdmTypeEnum.NEW.getValue().equals(request.getSpecificity())) {
				request.setGnssMode(GnssModeEnum.SINGLEBEIDOU.getValue());
			}
			request.setCreateTime(new Date());
			request.setDeleted(0);
			bdmMonitDevice = this.init(request);
			//同步到待分配终端对象实体表
			QueryWrapper<BdmVirtualTarget> wrapper = new QueryWrapper<>();
			wrapper.eq("number", bdmMonitDevice.getUniqueId());
			wrapper.last("LIMIT 1");
			BdmVirtualTarget latestRecord = bdmVirtualTargetService.getOne(wrapper);
			BdmVirtualTarget virtualTarget = new BdmVirtualTarget();
			BeanUtils.copyProperties(bdmMonitDevice, virtualTarget);
			virtualTarget.setTargetType(0);
			virtualTarget.setDeviceCode(bdmMonitDevice.getDeviceNum());
			virtualTarget.setNumber(bdmMonitDevice.getUniqueId());
			if (latestRecord == null) {
				virtualTarget.setId(targetId.nextId());
				virtualTarget.setCreateAccount(AuthUtil.getUserAccount());
				boolean saveRes = bdmVirtualTargetService.save(virtualTarget);
				if (saveRes) {
					log.info("bdmVirtualTargetService漏掉的 新增成功");
				} else {
					log.info("bdmVirtualTargetService漏掉的 新增失败:{}", virtualTarget);
				}
			} else {
				virtualTarget.setId(latestRecord.getId());
				boolean updateRes = bdmVirtualTargetService.updateById(virtualTarget);
				if (updateRes) {
					log.info("bdmVirtualTargetService 更新成功");
				} else {
					log.info("bdmVirtualTargetService 更新失败:{}", virtualTarget);
				}
			}
			QueryWrapper<BdmAbstractTarget> batWrapper = new QueryWrapper<>();
			batWrapper.eq("number", bdmMonitDevice.getUniqueId());
			batWrapper.last("LIMIT 1");
			BdmAbstractTarget batLatestRecord = bdmAbstractTargetService.getOne(batWrapper);
			//同步到抽象监控对象实体表
			BdmAbstractTarget bdmAbstractTarget = new BdmAbstractTarget();
			bdmAbstractTarget.setId(virtualTarget.getId());
			bdmAbstractTarget.setNumber(bdmMonitDevice.getUniqueId());
			bdmAbstractTarget.setName(bdmMonitDevice.getUniqueId());
			bdmAbstractTarget.setTargetType(0);
			bdmAbstractTarget.setDeptId(bdmMonitDevice.getDeptId());
			bdmAbstractTarget.setCategory(0);
			bdmAbstractTarget.setDeleted(0);
			if (batLatestRecord == null) {
				boolean saveRes = bdmAbstractTargetService.save(bdmAbstractTarget);
				if (saveRes) {
					log.info("bdmAbstractTargetService漏掉的 新增成功");
				} else {
					log.info("bdmAbstractTargetService漏掉的 新增失败:{}", bdmAbstractTarget);
				}
			} else {
				boolean updateRes = bdmAbstractTargetService.updateById(bdmAbstractTarget);
				if (updateRes) {
					log.info("bdmAbstractTargetService 更新成功");
				} else {
					log.info("bdmAbstractTargetService 更新失败:{}", bdmAbstractTarget);
				}
			}
			bdmMonitDevice.setTargetId(latestRecord.getId());
			baseMapper.update(bdmMonitDevice);
			//同步到抽象监控对象实体表
			BdmAbstractDevice abstractDevice = new BdmAbstractDevice();
			BeanUtils.copyProperties(bdmMonitDevice, abstractDevice);
			abstractDevice.setTargetType(0);
			abstractDevice.setDeleted(0);
			bdmAbstractDeviceService.updateById(abstractDevice);
		} else {
			if (request.getSpecificity().equals(BdmTypeEnum.OLD.getValue())) {
				R<String> r1 = deviceNumUtils.deviceNumProducer(ProductTypeEnum.O.name());
				if (r1.isSuccess()) {
					request.setDeviceNum(r1.getData());
				}
			}
			if (BdmTypeEnum.SPECIFICITY.getValue().equals(request.getSpecificity())) {
				R<String> r1 = deviceNumUtils.deviceNumProducer(ProductTypeEnum.S.name());
				if (r1.isSuccess()) {
					request.setDeviceNum(r1.getData());
				}
			}
			SnowflakeIdWorker idWorker = new SnowflakeIdWorker(BaseInfoConstants.MONIT_DEVICE_TYPE, BaseInfoConstants.ZERO, BaseInfoConstants.ZERO);
			request.setId(idWorker.nextId());
			request.setCreateTime(new Date());
			if (BdmTypeEnum.NEW.getValue().equals(request.getSpecificity())) {
				request.setGnssMode(GnssModeEnum.SINGLEBEIDOU.getValue());
			}
			bdmMonitDevice = this.init(request);
			String account = AuthUtil.getUserAccount();
			bdmMonitDevice.setCreateAccount(account);
			//同步到待分配终端对象实体表
			BdmVirtualTarget virtualTarget = new BdmVirtualTarget();
			BeanUtils.copyProperties(bdmMonitDevice, virtualTarget);
			virtualTarget.setId(targetId.nextId());
			virtualTarget.setTargetType(0);
			virtualTarget.setDeviceCode(bdmMonitDevice.getDeviceNum());
			virtualTarget.setNumber(bdmMonitDevice.getUniqueId());
			virtualTarget.setCreateAccount(AuthUtil.getUserAccount());
			bdmVirtualTargetService.save(virtualTarget);
			// 同步到抽象监控对象表
			BdmAbstractTarget bdmAbstractTarget = new BdmAbstractTarget();
			bdmAbstractTarget.setId(virtualTarget.getId());
			bdmAbstractTarget.setNumber(bdmMonitDevice.getUniqueId());
			bdmAbstractTarget.setName(bdmMonitDevice.getUniqueId());
			bdmAbstractTarget.setTargetType(0);
			bdmAbstractTarget.setDeptId(bdmMonitDevice.getDeptId());
			bdmAbstractTarget.setCategory(0);
			bdmAbstractTarget.setDeleted(0);
			bdmAbstractTarget.setCreateAccount(AuthUtil.getUserAccount());
			bdmAbstractTargetService.save(bdmAbstractTarget);
			bdmMonitDevice.setTargetId(virtualTarget.getId());
			this.monitDeviceMapper.insertMonit(bdmMonitDevice);
			//同步到抽象监控对象实体表
			BdmAbstractDevice abstractDevice = new BdmAbstractDevice();
			BeanUtils.copyProperties(bdmMonitDevice, abstractDevice);
			abstractDevice.setTargetType(0);
			abstractDevice.setDeleted(0);
			abstractDevice.setCreateAccount(AuthUtil.getUserAccount());
			bdmAbstractDeviceService.saveDevice(abstractDevice);
		}

		if (request.getNumbers() != null && !request.getNumbers().isEmpty()) {
			//更新存在且未绑定的物联网卡信息
			iotCardService.updateIotCardByDeviceId(request.getNumbers(), bdmMonitDevice.getId(), bdmMonitDevice.getDeviceType());
		}

		if (bdmMonitDevice.getSpecificity().equals(BdmTypeEnum.NEW.getValue())) {
			//跟新bdm_terminal
			terminalService.updateFormalByDeviceSeq(request.getUniqueId());
			//更新bdm_device_code
			deviceCodeService.updateActivated(request.getDeviceNum());
		}

		BdmMonitDevice monitDevice = getBaseMapper().selectById(bdmMonitDevice.getId());

		Map<String, String> map = new HashMap<>();
		String key = monitDevice.getDeviceType() + "-" + monitDevice.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("uniqueId", monitDevice.getUniqueId());
		innerMap.put("deviceNum", monitDevice.getDeviceNum());
		innerMap.put("category", monitDevice.getCategory());
		innerMap.put("deviceType", monitDevice.getDeviceType());
		innerMap.put("deptId", monitDevice.getDeptId());
		innerMap.put("iotProtocol", monitDevice.getIotProtocol());
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		//todo：Map<String, String> map这样写go那边解析有问题
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);

		if (BdmTypeEnum.NEW.getValue().equals(request.getSpecificity())) {
			Map<String, String> targetMap = new HashMap<>();
			String targetKey = TargetTypeEnum.ENDPOINTS.getSymbol() + "-" + monitDevice.getId();
			Map<String, Object> targetInnerMap = new HashMap<>();
			targetInnerMap.put("targetName", monitDevice.getUniqueId());
			targetInnerMap.put("targetType", 0);
			targetInnerMap.put("deptId", monitDevice.getDeptId());
			try {
				map.put(targetKey, new ObjectMapper().writeValueAsString(targetInnerMap));
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			//todo：这样写go那边解析有问题
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, targetMap);
		}

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.INSERT);
		deviceInfo.setDeviceId(monitDevice.getId());
		deviceInfo.setDeviceType(monitDevice.getDeviceType());
		deviceInfo.setDeviceUniqueId(monitDevice.getUniqueId());
		deviceInfo.setDeviceModel(monitDevice.getModel());
		deviceInfo.setDeviceNum(monitDevice.getDeviceNum());
		deviceInfo.setDeptId(monitDevice.getDeptId());
		deviceInfo.setSpecificity(monitDevice.getSpecificity());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_monit_device", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("监测终端信息更新消息发送到kafka失败", e);
		}
		//messageClient
		try {
			messageClient.terminal(CommonConstant.OPER_TYPE_ADD, monitDevice.getDeviceType(), monitDevice);
		} catch (Exception e) {
			log.error("消息发送到messageClient失败：" + e.getMessage());
		}
		return monitDevice;
	}

	@Override
	public BdmMonitDevice insertMonitDevice(MonitDeviceRequest request) {
		BdmMonitDevice bdmMonitDevice = new BdmMonitDevice();
		QueryWrapper<BdmMonitDevice> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("unique_id", request.getUniqueId());
		BdmMonitDevice monit = baseMapper.selectOne(queryWrapper);
		TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(BaseInfoConstants.VIRTUAL_TARGET_TYPE, BaseInfoConstants.ZERO, BaseInfoConstants.ZERO);
		if (monit != null) {
			request.setId(monit.getId());
			request.setCreateTime(new Date());
			request.setDeleted(0);
			bdmMonitDevice = this.init(request);
			//同步到待分配终端对象实体表
			QueryWrapper<BdmVirtualTarget> wrapper = new QueryWrapper<>();
			wrapper.eq("number", bdmMonitDevice.getUniqueId());
			wrapper.last("LIMIT 1");
			BdmVirtualTarget latestRecord = bdmVirtualTargetService.getOne(wrapper);
			BdmVirtualTarget virtualTarget = new BdmVirtualTarget();
			BeanUtils.copyProperties(bdmMonitDevice, virtualTarget);
			virtualTarget.setTargetType(0);
			virtualTarget.setDeviceCode(bdmMonitDevice.getDeviceNum());
			virtualTarget.setNumber(bdmMonitDevice.getUniqueId());
			if (latestRecord == null) {
				virtualTarget.setId(targetId.nextId());
				virtualTarget.setCreateAccount(AuthUtil.getUserAccount());
				boolean saveRes = bdmVirtualTargetService.save(virtualTarget);
				if (saveRes) {
					log.info("bdmVirtualTargetService漏掉的 新增成功");
				} else {
					log.info("bdmVirtualTargetService漏掉的 新增失败:{}", virtualTarget);
				}
			} else {
				virtualTarget.setId(latestRecord.getId());
				boolean updateRes = bdmVirtualTargetService.updateById(virtualTarget);
				if (updateRes) {
					log.info("bdmVirtualTargetService 更新成功");
				} else {
					log.info("bdmVirtualTargetService 更新失败:{}", virtualTarget);
				}
			}
			QueryWrapper<BdmAbstractTarget> batWrapper = new QueryWrapper<>();
			batWrapper.eq("number", bdmMonitDevice.getUniqueId());
			batWrapper.last("LIMIT 1");
			BdmAbstractTarget batLatestRecord = bdmAbstractTargetService.getOne(batWrapper);
			//同步到抽象监控对象实体表
			BdmAbstractTarget bdmAbstractTarget = new BdmAbstractTarget();
			bdmAbstractTarget.setId(virtualTarget.getId());
			bdmAbstractTarget.setNumber(bdmMonitDevice.getUniqueId());
			bdmAbstractTarget.setName(bdmMonitDevice.getUniqueId());
			bdmAbstractTarget.setTargetType(0);
			bdmAbstractTarget.setDeptId(bdmMonitDevice.getDeptId());
			bdmAbstractTarget.setCategory(0);
			bdmAbstractTarget.setDeleted(0);
			if (batLatestRecord == null) {
				boolean saveRes = bdmAbstractTargetService.save(bdmAbstractTarget);
				if (saveRes) {
					log.info("bdmAbstractTargetService漏掉的 新增成功");
				} else {
					log.info("bdmAbstractTargetService漏掉的 新增失败:{}", bdmAbstractTarget);
				}
			} else {
				boolean updateRes = bdmAbstractTargetService.updateById(bdmAbstractTarget);
				if (updateRes) {
					log.info("bdmAbstractTargetService 更新成功");
				} else {
					log.info("bdmAbstractTargetService 更新失败:{}", bdmAbstractTarget);
				}
			}
			bdmMonitDevice.setTargetId(latestRecord.getId());
			baseMapper.update(bdmMonitDevice);
		} else {
			SnowflakeIdWorker idWorker = new SnowflakeIdWorker(BaseInfoConstants.MONIT_DEVICE_TYPE, BaseInfoConstants.ZERO, BaseInfoConstants.ZERO);
			request.setId(idWorker.nextId());
			request.setCreateTime(new Date());
			if (BdmTypeEnum.NEW.getValue().equals(request.getSpecificity())) {
				request.setGnssMode(GnssModeEnum.SINGLEBEIDOU.getValue());
			}
			bdmMonitDevice = this.init(request);
			String account = AuthUtil.getUserAccount();
			bdmMonitDevice.setCreateAccount(account);
			//同步到待分配终端对象实体表
			BdmVirtualTarget virtualTarget = new BdmVirtualTarget();
			BeanUtils.copyProperties(bdmMonitDevice, virtualTarget);
			virtualTarget.setId(null);
			virtualTarget.setTargetType(0);
			virtualTarget.setDeviceCode(bdmMonitDevice.getDeviceNum());
			virtualTarget.setNumber(bdmMonitDevice.getUniqueId());
			virtualTarget.setCreateAccount(AuthUtil.getUserAccount());
			bdmVirtualTargetService.save(virtualTarget);
			// 同步到抽象监控对象表
			BdmAbstractTarget bdmAbstractTarget = new BdmAbstractTarget();
			bdmAbstractTarget.setId(virtualTarget.getId());
			bdmAbstractTarget.setNumber(bdmMonitDevice.getUniqueId());
			bdmAbstractTarget.setName(bdmMonitDevice.getUniqueId());
			bdmAbstractTarget.setTargetType(0);
			bdmAbstractTarget.setDeptId(bdmMonitDevice.getDeptId());
			bdmAbstractTarget.setCategory(0);
			bdmAbstractTarget.setDeleted(0);
			bdmAbstractTarget.setCreateAccount(AuthUtil.getUserAccount());
			bdmAbstractTargetService.save(bdmAbstractTarget);
			bdmMonitDevice.setTargetId(virtualTarget.getId());
			bdmMonitDevice.setId(null);
			this.monitDeviceMapper.insert(bdmMonitDevice);
		}
		if (request.getNumbers() != null && !request.getNumbers().isEmpty()) {
			//更新存在且未绑定的物联网卡信息
			iotCardService.updateIotCardByDeviceId(request.getNumbers(), bdmMonitDevice.getId(), bdmMonitDevice.getDeviceType());
		}

		if (bdmMonitDevice.getSpecificity().equals(BdmTypeEnum.NEW.getValue())) {
			//跟新bdm_terminal
			terminalService.updateFormalByDeviceSeq(request.getUniqueId());
			//更新bdm_device_code
			deviceCodeService.updateActivated(request.getDeviceNum());
		}
		BdmMonitDevice monitDevice = getBaseMapper().selectById(bdmMonitDevice.getId());

		Map<String, String> map = new HashMap<>();
		String key = monitDevice.getDeviceType() + "-" + monitDevice.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("uniqueId", monitDevice.getUniqueId());
		innerMap.put("deviceNum", monitDevice.getDeviceNum());
		innerMap.put("category", monitDevice.getCategory());
		innerMap.put("deviceType", monitDevice.getDeviceType());
		innerMap.put("deptId", monitDevice.getDeptId());
		innerMap.put("iotProtocol", monitDevice.getIotProtocol());
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		//todo：Map<String, String> map这样写go那边解析有问题
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);

		if (BdmTypeEnum.NEW.getValue().equals(request.getSpecificity())) {
			Map<String, String> targetMap = new HashMap<>();
			String targetKey = TargetTypeEnum.ENDPOINTS.getSymbol() + "-" + monitDevice.getId();
			Map<String, Object> targetInnerMap = new HashMap<>();
			targetInnerMap.put("targetName", monitDevice.getUniqueId());
			targetInnerMap.put("targetType", 0);
			targetInnerMap.put("deptId", monitDevice.getDeptId());
			try {
				map.put(targetKey, new ObjectMapper().writeValueAsString(targetInnerMap));
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			//todo：这样写go那边解析有问题
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, targetMap);
		}

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.INSERT);
		deviceInfo.setDeviceId(monitDevice.getId());
		deviceInfo.setDeviceType(monitDevice.getDeviceType());
		deviceInfo.setDeviceUniqueId(monitDevice.getUniqueId());
		deviceInfo.setDeviceModel(monitDevice.getModel());
		deviceInfo.setDeviceNum(monitDevice.getDeviceNum());
		deviceInfo.setDeptId(monitDevice.getDeptId());
		deviceInfo.setSpecificity(monitDevice.getSpecificity());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_monit_device", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("监测终端信息更新消息发送到kafka失败", e);
		}
		//messageClient
		try {
			messageClient.terminal(CommonConstant.OPER_TYPE_ADD, monitDevice.getDeviceType(), monitDevice);
		} catch (Exception e) {
			log.error("消息发送到messageClient失败：" + e.getMessage());
		}
		return monitDevice;
	}

	/**
	 * 修改数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public BdmMonitDevice update(MonitDeviceRequest request) {

		BdmMonitDevice bdmMonitDevice = this.init(request);
		this.monitDeviceMapper.update(bdmMonitDevice);

		iotCardService.updateDeviceIdById(request.getId(), request.getDeviceType(), request.getNumbers());

		if (request.getNumbers() != null && !request.getNumbers().isEmpty()) {
			iotCardService.updateIotCardByDeviceId(request.getNumbers(), request.getId(), request.getDeviceType());
		}

		BdmMonitDevice monitDevice = getBaseMapper().selectById(bdmMonitDevice.getId());

		// 未绑定的非新设备更新抽象终端表 TODO 是否需要判断是否已经绑定
		if (monitDevice.getSpecificity() != BdmTypeEnum.NEW.getValue()) {
			BdmAbstractDevice abstractDevice = new BdmAbstractDevice();
			BeanUtils.copyProperties(monitDevice, abstractDevice);
			bdmAbstractDeviceService.updateById(abstractDevice);
		}

		// 更新Redis缓存
		String key = monitDevice.getDeviceType() + "-" + monitDevice.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("uniqueId", monitDevice.getUniqueId());
		innerMap.put("deviceNum", monitDevice.getDeviceNum());
		innerMap.put("category", monitDevice.getCategory());
		innerMap.put("deviceType", monitDevice.getDeviceType());
		innerMap.put("deptId", monitDevice.getDeptId());
		innerMap.put("iotProtocol", monitDevice.getIotProtocol());
		Map<String, String> map = new HashMap<>();
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		// todo 这样写go那边解析有问题
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.UPDATE);
		deviceInfo.setDeviceId(monitDevice.getId());
		deviceInfo.setDeviceType(monitDevice.getDeviceType());
		deviceInfo.setDeviceUniqueId(monitDevice.getUniqueId());
		deviceInfo.setDeviceModel(monitDevice.getModel());
		deviceInfo.setDeviceNum(monitDevice.getDeviceNum());
		deviceInfo.setDeptId(monitDevice.getDeptId());
		deviceInfo.setSpecificity(monitDevice.getSpecificity());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_monit_device", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("监测终端信息更新消息发送到kafka失败", e);
		}
		//messageClient
		try {
			messageClient.terminal(CommonConstant.OPER_TYPE_ADD, monitDevice.getDeviceType(), monitDevice);
		} catch (Exception e) {
			log.error("消息发送到messageClient失败：" + e.getMessage());
		}
		return monitDevice;
	}

	@Override
	public BdmMonitDevice updateMonitDevice(MonitDeviceRequest request) {
		BdmMonitDevice bdmMonitDevice = this.init(request);
		this.monitDeviceMapper.update(bdmMonitDevice);

		iotCardService.updateDeviceIdById(request.getId(), request.getDeviceType(), request.getNumbers());

		if (request.getNumbers() != null && !request.getNumbers().isEmpty()) {
			iotCardService.updateIotCardByDeviceId(request.getNumbers(), request.getId(), request.getDeviceType());
		}

		BdmMonitDevice monitDevice = getBaseMapper().selectById(bdmMonitDevice.getId());

		// 更新Redis缓存
		String key = monitDevice.getDeviceType() + "-" + monitDevice.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("uniqueId", monitDevice.getUniqueId());
		innerMap.put("deviceNum", monitDevice.getDeviceNum());
		innerMap.put("category", monitDevice.getCategory());
		innerMap.put("deviceType", monitDevice.getDeviceType());
		innerMap.put("deptId", monitDevice.getDeptId());
		innerMap.put("iotProtocol", monitDevice.getIotProtocol());
		Map<String, String> map = new HashMap<>();
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		// todo 这样写go那边解析有问题
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.UPDATE);
		deviceInfo.setDeviceId(monitDevice.getId());
		deviceInfo.setDeviceType(monitDevice.getDeviceType());
		deviceInfo.setDeviceUniqueId(monitDevice.getUniqueId());
		deviceInfo.setDeviceModel(monitDevice.getModel());
		deviceInfo.setDeviceNum(monitDevice.getDeviceNum());
		deviceInfo.setDeptId(monitDevice.getDeptId());
		deviceInfo.setSpecificity(monitDevice.getSpecificity());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_monit_device", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("监测终端信息更新消息发送到kafka失败", e);
		}
		//messageClient
		try {
			messageClient.terminal(CommonConstant.OPER_TYPE_ADD, monitDevice.getDeviceType(), monitDevice);
		} catch (Exception e) {
			log.error("消息发送到messageClient失败：" + e.getMessage());
		}
		return monitDevice;
	}

	/**
	 * 通过主键删除数据
	 *
	 * @param id 主键
	 * @return 是否成功
	 */
	@Override
	public boolean deleteById(Long id) {
		return this.monitDeviceMapper.deleteById(id) > 0;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean deleteByIds(Long[] ids) {

		QueryWrapper<BdmMonitDevice> wrapper = new QueryWrapper<>();
		wrapper.in("id", ids);
		List<BdmMonitDevice> list = this.monitDeviceMapper.selectList(wrapper);

		List<String> deviceNumList = new ArrayList<>();
		List<String> uniqueIdList = new ArrayList<>();

		if (!list.isEmpty()) {
			// 查询 deviceNum 列表
			deviceNumList = list.stream()
				.filter(request -> BdmTypeEnum.NEW.getValue().equals(request.getSpecificity()))
				.map(BdmMonitDevice::getDeviceNum)
				.collect(Collectors.toList());

			// 查询 uniqueId 列表
			uniqueIdList = list.stream()
				.filter(request -> BdmTypeEnum.NEW.getValue().equals(request.getSpecificity()))
				.map(BdmMonitDevice::getUniqueId)
				.collect(Collectors.toList());
		}

		boolean result = this.monitDeviceMapper.deleteByIds(ids) > 0;
		if (result) {
			for (Long id : ids) {
				String key = BaseInfoConstants.MONIT_DEVICE_TYPE + "-" + id;
				redisTemplate.delete(BaseInfoConstants.BASEINFO_DEVICE + ":" + key);
			}
			this.iotCardService.deleteByDeviceIds(ids, DeviceTypeEnum.MONIT.getSymbol());
			if (!uniqueIdList.isEmpty()) {
				//跟新bdm_terminal
				terminalService.updateFormalByDeviceSeqs(uniqueIdList);
			}
			if (!deviceNumList.isEmpty()) {
				//更新bdm_device_code
				deviceCodeService.updateActivatedByDeviceNum(deviceNumList);
			}

			DeviceInfo deviceInfo = new DeviceInfo();
			deviceInfo.setOperation(Operation.DELETE);
			Long lastId = ids[ids.length - 1];
			deviceInfo.setDeviceId(lastId);
			deviceInfo.setDeviceIds(CollUtil.newHashSet(ids));
			try {
				this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_monit_device", JSON.toJSONString(deviceInfo));
			} catch (Exception e) {
				log.error("监测终端信息更新消息发送到kafka失败", e);
			}
			//messageClient
			try {
				List<Object> terminals = new ArrayList<>(list);
				messageClient.terminalBatch(CommonConstant.OPER_TYPE_DELETE, BaseInfoConstants.MONIT_DEVICE_TYPE, terminals);
			} catch (Exception e) {
				log.error("消息发送到messageClient失败：" + e.getMessage());
			}

			//同步更新抽象监控对象实体表
			bdmAbstractDeviceService.deleteByIds(ids);
			//同步更新待分配终端对象实体表
			if (!uniqueIdList.isEmpty()) {
				bdmVirtualTargetService.updateByUniqueId(uniqueIdList);

				QueryWrapper<BdmVirtualTarget> virtualTargetQueryWrapper = new QueryWrapper<>();
				if (!uniqueIdList.isEmpty()) {
					MybatisPlusWrapperUitls.longListIn(wrapper, "unique_id", uniqueIdList);
				}
				virtualTargetQueryWrapper.select("id");
				List<Long> idList = bdmVirtualTargetService.getBaseMapper()
					.selectList(virtualTargetQueryWrapper)
					.stream()
					.map(BdmVirtualTarget::getId)
					.collect(Collectors.toList());
				// 同步更新抽象监控对象实体表
				if (!idList.isEmpty()) {
					bdmAbstractTargetService.deleteByIds(idList.toArray(new Long[0]));
					for (Long id : idList) {
						String targetKey = BaseInfoConstants.VIRTUAL_TARGET_TYPE + "-" + id;
						redisTemplate.delete(BaseInfoConstants.BASEINFO_TARGET + ":" + targetKey);
					}
				}
			}
		}
		return result;
	}

	@Override
	public IPage<FacilityNoBingResponse> selectNoBind(DeviceNoBindRequest deviceNoBindRequest, String account, String deptIds) {
		Page page = new Page();
		page.setCurrent(deviceNoBindRequest.getCurrent());
		page.setSize(deviceNoBindRequest.getSize());
		return this.monitDeviceMapper.selectNoBind(page, deviceNoBindRequest, account, deptIds);
	}

	@Override
	public List<FacilityNoBingResponse> selectBindByFacilityId(Long id, Integer targetType, DeviceNoBindRequest request) {
		return this.monitDeviceMapper.selectBindByFacilityId(id, targetType, request);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public List<MonitDeviceRequest> importExcel(List<MonitDeviceRequest> list, Long userId) {
		List<MonitDeviceRequest> duplicateRequests = getDuplicateRequests(list);
		List<MonitDeviceRequest> requests = new ArrayList<>(list);
		// 移除duplicateRequests中的所有元素
		requests.removeIf(request -> duplicateRequests.contains(request));
		if (!requests.isEmpty()) {
			List<MonitDeviceRequest> filteredList = processRequests(requests, userId, duplicateRequests);
			if (!filteredList.isEmpty()) {
				List<Long> arrayList = updateDatabaseAndCache(filteredList);
				sendNotifications(filteredList, arrayList);
			}
		}
		return duplicateRequests;
	}

	/**
	 * 重复数据
	 *
	 * @param list
	 * @return
	 */
	public List<MonitDeviceRequest> getDuplicateRequests(List<MonitDeviceRequest> list) {
		// 1. 统计每个number出现的次数
		List<String> numberList = list.stream()
			.map(MonitDeviceRequest::getNumbers)
			.filter(Objects::nonNull) // 确保numbers不为空
			.filter(number -> !number.isEmpty()) // 确保numbers不为空字符串
			.collect(Collectors.toList());

		// 2. 过滤出重复的number
		List<String> duplicates = numberList.stream()
			.filter(number -> Collections.frequency(numberList, number) > 1)
			.distinct()
			.collect(Collectors.toList());

		List<String> uniqueIdList = list.stream()
			.map(MonitDeviceRequest::getUniqueId)
			.collect(Collectors.toList());

		List<String> duplicateUniqueIds = uniqueIdList.stream()
			.filter(id -> Collections.frequency(uniqueIdList, id) > 1)
			.distinct()
			.collect(Collectors.toList());

		return list.stream()
			.filter(monitDeviceRequest -> duplicates.contains(monitDeviceRequest.getNumbers()) || duplicateUniqueIds.contains(monitDeviceRequest.getUniqueId()))
			.peek(monitDeviceRequest -> {
				if (duplicates.contains(monitDeviceRequest.getNumbers())) {
					monitDeviceRequest.setMsg("导入的数据物联网卡号重复");
				}
				if (duplicateUniqueIds.contains(monitDeviceRequest.getUniqueId())) {
					monitDeviceRequest.setMsg("导入的数据序列号重复");
				}
			})
			.collect(Collectors.toList());
	}

	/**
	 * 数据处理
	 */
	private List<MonitDeviceRequest> processRequests(List<MonitDeviceRequest> requests, Long userId, List<MonitDeviceRequest> duplicateRequests) {
		// 按部门ID分组
		Map<Long, List<MonitDeviceRequest>> groupedByDept = requests.stream()
			.collect(Collectors.groupingBy(MonitDeviceRequest::getDeptId));

		List<MonitDeviceRequest> filterList = new ArrayList<>();

		for (Map.Entry<Long, List<MonitDeviceRequest>> entry : groupedByDept.entrySet()) {
			// TODO 终端赋码号不存在！和 序列号已存在！可连表查进行判断 检查输入的赋码号的合法性
			Map<String, BdmDeviceLedger> deviceLedgerMap = getDeviceLedgers(entry.getKey())
				.stream()
				.collect(Collectors.toMap(BdmDeviceLedger::getDeviceNum, ledger -> ledger));

			List<MonitDeviceRequest> requestsForDept = entry.getValue();

			// 过滤出该部门下设备编号存在于deviceLedgers中的请求
			List<MonitDeviceRequest> validRequestsForDept = requestsForDept.stream()
				.filter(request -> {
					if (!BdmTypeEnum.NEW.getValue().equals(request.getSpecificity())) {
						return true;
					} else {
						return isValidRequest(request, deviceLedgerMap);
					}
				})
				.collect(Collectors.toList());

			filterList.addAll(validRequestsForDept);

			//检查输入的赋码号的合法性
			requestsForDept.stream()
				.filter(request -> BdmTypeEnum.NEW.getValue().equals(request.getSpecificity()) && !deviceLedgerMap.containsKey(request.getDeviceNum()))
				.peek(request -> request.setMsg("终端赋码号不存在！"))
				.forEach(duplicateRequests::add);
		}

		if (!filterList.isEmpty()) {

			List<String> uniqueIdList = filterList.stream()
				.filter(Objects::nonNull)
				.map(MonitDeviceRequest::getUniqueId)
				.collect(Collectors.toList());

			QueryWrapper<BdmMonitDevice> wrapper = new QueryWrapper<>();
			wrapper.eq("deleted", 0);
			if (!uniqueIdList.isEmpty()) {
				MybatisPlusWrapperUitls.longListIn(wrapper, "unique_id", uniqueIdList);
			}
			wrapper.select("unique_id");

			//序列号判重
			List<String> uniqueIds = baseMapper.selectList(wrapper)
				.stream()
				.map(BdmMonitDevice::getUniqueId)
				.collect(Collectors.toList());

			List<MonitDeviceRequest> resultList = filterList.stream()
				.filter(monitDeviceRequest ->
					monitDeviceRequest.getUniqueId() != null && !uniqueIds.contains(monitDeviceRequest.getUniqueId()))
				.collect(Collectors.toList());

			filterList.stream()
				.filter(request -> !resultList.contains(request))
				.peek(request -> request.setMsg("序列号已存在！"))
				.forEach(duplicateRequests::add);

			filterList = resultList;
			if (!filterList.isEmpty()) {
				List<String> numberedList = resultList.stream()
					.filter(Objects::nonNull)
					.map(MonitDeviceRequest::getNumbers)
					.collect(Collectors.toList());

				List<String> numberList = new ArrayList<>();

				if (!numberedList.isEmpty()) {

					log.info("[importExcel]终端信息导入，将要判断是否是底座登录");
					DataAuthCE ceDataAuth = ceTokenUtil.getDataAuth();
					boolean isCE = ceTokenUtil.isCELogin();
					if(isCE){
						log.info("[importExcel]终端信息导入，国能底座登录");
						//如果是国能底座，则查询数据权限范围内的物联网卡信息
						String account = ceDataAuth.getAccount();
						String deptArrayStr = ceDataAuth.getOrgListStr();
						numberList = iotCardService.queryIotCE(numberedList, account, deptArrayStr);
						log.info("[importExcel]终端信息导入，获取数据权限范围内的物联网卡数量为：{}", numberList==null?0:numberList.size());
					}else{
						userId = AuthUtil.isAdministrator() ? null : userId;
						// 物联网卡验证
						numberList = iotCardService.queryIot(numberedList, userId);
					}
				}

				final List<String> finalNumberList = numberList;

				List<MonitDeviceRequest> filteredList = resultList.stream()
					.filter(monitDeviceRequest ->
						monitDeviceRequest.getNumbers() == null || finalNumberList.contains(monitDeviceRequest.getNumbers()))
					.collect(Collectors.toList());

				resultList.stream()
					.filter(request -> !filteredList.contains(request))
					.peek(request -> request.setMsg("该物联网卡号不存在或已绑定！"))
					.forEach(duplicateRequests::add);
				filterList = filteredList;
			}
		}

		return filterList;
	}

	/**
	 * 查询出库到本单位数据
	 */
	private List<BdmDeviceLedger> getDeviceLedgers(Long deptId) {
		return deviceLedgerService.getBaseMapper()
			.selectList(new QueryWrapper<BdmDeviceLedger>()
				.eq("device_type", BaseInfoConstants.MONIT_DEVICE_TYPE)
				.eq("user_dept_id", deptId));
	}

	/**
	 * 新设备给设置正确的赋码信息
	 */
	private boolean isValidRequest(MonitDeviceRequest request, Map<String, BdmDeviceLedger> targetMap) {
		BdmDeviceLedger deviceLedger = targetMap.get(request.getDeviceNum());
		if (deviceLedger != null) {
			request.setModel(deviceLedger.getModel());
			request.setImei(deviceLedger.getImei());
			request.setVendor(deviceLedger.getVendor());
			request.setUniqueId(deviceLedger.getUniqueId());
			request.setBdChipSn(deviceLedger.getBdChipSn());
			request.setDeviceType(deviceLedger.getDeviceType());
			request.setCategory(deviceLedger.getCategory());
			return true;
		}
		return false;
	}

	/**
	 * 更新数据库和进行数据redis缓存
	 */
	private List<Long> updateDatabaseAndCache(List<MonitDeviceRequest> filteredList) {

		SnowflakeIdWorker idWorker = new SnowflakeIdWorker(BaseInfoConstants.MONIT_DEVICE_TYPE, BaseInfoConstants.ZERO, BaseInfoConstants.ZERO);
		TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(BaseInfoConstants.VIRTUAL_TARGET_TYPE, BaseInfoConstants.ZERO, BaseInfoConstants.ZERO);
		Map<String, BdmMonitDevice> monitMap = baseMapper.selectList(new QueryWrapper<BdmMonitDevice>().eq("deleted", 1))
			.stream()
			.collect(Collectors.toMap(BdmMonitDevice::getUniqueId, MonitDevice -> MonitDevice));
		//用于判断恢复被删除的设备时，判断是执行新增还是恢复操作
		Map<String, BdmVirtualTarget> allVirtualTargetMap = bdmVirtualTargetService.list()
			.stream()
			.collect(Collectors.toMap(BdmVirtualTarget::getNumber, VirtualTarget -> VirtualTarget, (existing, replacement) -> existing));
		Map<String, BdmAbstractTarget> allBatMap = bdmAbstractTargetService.list()
			.stream()
			.collect(Collectors.toMap(BdmAbstractTarget::getNumber, bat -> bat, (existing, replacement) -> existing));
		// 创建一个 Map 来存储所有的键值对
		List<TerminalIotRequest> terminalIotRequests = new ArrayList<>();
		Map<String, String> map = new HashMap<>();
		Map<String, String> targrtMap = new HashMap<>();
		List<Long> arrayList = new ArrayList<>();
		List<BdmVirtualTarget> virtualTargets = new ArrayList<>();
		List<BdmAbstractDevice> abstractDevices = new ArrayList<>();
		List<BdmAbstractTarget> abstractTargets = new ArrayList<>();
		// 在处理filteredList之前，收集所有新设备的序列号
		Set<String> newDeviceUniqueIds = filteredList.stream()
			.filter(request -> BdmTypeEnum.NEW.getValue().equals(request.getSpecificity()))
			.map(MonitDeviceRequest::getUniqueId)
			.collect(Collectors.toSet());
		Map<String, BdmVirtualTarget> virtualTargetMap = new HashMap<>();
		// 一次性查询所有匹配的BdmVirtualTarget记录
		if (!newDeviceUniqueIds.isEmpty()) {
			List<BdmVirtualTarget> allVirtualTargets = bdmVirtualTargetService.list(
				new QueryWrapper<BdmVirtualTarget>().in("number", newDeviceUniqueIds).select("id").select("number")
			);
			// 将查询结果存储在一个Map中，以便后续快速查找
			virtualTargetMap = allVirtualTargets.stream().collect(Collectors.toMap(BdmVirtualTarget::getNumber, Function.identity()));
		}
		for (MonitDeviceRequest request : filteredList) {
			BdmMonitDevice device = monitMap.get(request.getUniqueId());
			// device为空新增，不为空修改
			long deviceId = (device == null) ? idWorker.nextId() : device.getId();
			request.setId(deviceId);
			request.setDeleted(0);
			//设备赋码
			if (device == null) {
				if (request.getSpecificity().equals(BdmTypeEnum.OLD.getValue())) {
					R<String> r1 = deviceNumUtils.deviceNumProducer(ProductTypeEnum.O.name());
					if (r1.isSuccess()) {
						request.setDeviceNum(r1.getData());
					}
				}
				if (BdmTypeEnum.SPECIFICITY.getValue().equals(request.getSpecificity())) {
					R<String> r1 = deviceNumUtils.deviceNumProducer(ProductTypeEnum.S.name());
					if (r1.isSuccess()) {
						request.setDeviceNum(r1.getData());
					}
				}
			} else {
				request.setDeviceNum(device.getDeviceNum());
			}
			//新设备的定位模式单独处理
			if (BdmTypeEnum.NEW.getValue().equals(request.getSpecificity())) {
				request.setGnssMode(GnssModeEnum.SINGLEBEIDOU.getValue());
			}
			request.setCreateTime(new Date());
			BdmMonitDevice bdmMonitDevice = this.init(request);

			BdmAbstractTarget bdmAbstractTarget = new BdmAbstractTarget();

			if (device == null) {
				//同步到待分配终端对象实体表
				BdmVirtualTarget virtualTarget = new BdmVirtualTarget();
				BeanUtils.copyProperties(bdmMonitDevice, virtualTarget, "id");
				virtualTarget.setId(targetId.nextId());
				virtualTarget.setTargetType(0);
				virtualTarget.setDeviceCode(bdmMonitDevice.getDeviceNum());
				virtualTarget.setNumber(bdmMonitDevice.getUniqueId());
				virtualTargets.add(virtualTarget);

				// TODO 同步到抽象监控对象实体表 abstractTargets
				bdmAbstractTarget.setId(virtualTarget.getId());
				bdmAbstractTarget.setNumber(bdmMonitDevice.getUniqueId());
				bdmAbstractTarget.setName(bdmMonitDevice.getUniqueId());
				bdmAbstractTarget.setTargetType(0);
				bdmAbstractTarget.setDeptId(bdmMonitDevice.getDeptId());
				bdmAbstractTarget.setCategory(0);
				bdmAbstractTarget.setDeleted(0);
				abstractTargets.add(bdmAbstractTarget);
				bdmMonitDevice.setTargetId(virtualTarget.getId());
				this.monitDeviceMapper.insertMonit(bdmMonitDevice);
				//同步到抽象监控对象实体表
				BdmAbstractDevice abstractDevice = new BdmAbstractDevice();
				BeanUtils.copyProperties(bdmMonitDevice, abstractDevice);
				//abstractDevice.setTargetId(0L);
				abstractDevice.setTargetType(0);
				abstractDevice.setDeleted(0);
				abstractDevices.add(abstractDevice);
			} else {
				//同步到待分配终端对象实体表
				BdmVirtualTarget virtualTarget = new BdmVirtualTarget();
				BeanUtils.copyProperties(bdmMonitDevice, virtualTarget, "id");
				virtualTarget.setTargetType(0);
				virtualTarget.setDeviceCode(bdmMonitDevice.getDeviceNum());
				virtualTarget.setNumber(bdmMonitDevice.getUniqueId());
				if (allVirtualTargetMap.containsKey(device.getUniqueId())) {
					virtualTarget.setId(virtualTargetMap.get(bdmMonitDevice.getUniqueId()).getId());
					bdmVirtualTargetService.updateById(virtualTarget);
				} else {
					virtualTarget.setId(targetId.nextId());
					virtualTargets.add(virtualTarget);
				}
				// TODO 更新到抽象监控对象实体表
				bdmAbstractTarget.setNumber(bdmMonitDevice.getUniqueId());
				bdmAbstractTarget.setName(bdmMonitDevice.getUniqueId());
				bdmAbstractTarget.setTargetType(0);
				bdmAbstractTarget.setDeptId(bdmMonitDevice.getDeptId());
				bdmAbstractTarget.setCategory(0);
				bdmAbstractTarget.setDeleted(0);
				bdmAbstractTarget.setId(virtualTarget.getId());
				if (allBatMap.containsKey(device.getUniqueId())) {
					bdmAbstractTargetService.updateById(bdmAbstractTarget);
				} else {
					abstractTargets.add(bdmAbstractTarget);
				}

				bdmMonitDevice.setTargetId(virtualTarget.getId());
				baseMapper.updateById(bdmMonitDevice);
				//同步到抽象监控对象实体表
				BdmAbstractDevice abstractDevice = new BdmAbstractDevice();
				BeanUtils.copyProperties(bdmMonitDevice, abstractDevice);
				abstractDevice.setTargetType(0);
				abstractDevice.setDeleted(0);
				bdmAbstractDeviceService.updateById(abstractDevice);
			}
			request.setId(bdmMonitDevice.getId());
			arrayList.add(bdmMonitDevice.getId());
			if (bdmMonitDevice.getNumbers() != null && !bdmMonitDevice.getNumbers().isEmpty()) {
				TerminalIotRequest terminalIotRequest = new TerminalIotRequest();
				terminalIotRequest.setId(bdmMonitDevice.getId());
				terminalIotRequest.setNumber(bdmMonitDevice.getNumbers());
				terminalIotRequest.setDeviceType(BaseInfoConstants.MONIT_DEVICE_TYPE);
				terminalIotRequests.add(terminalIotRequest);
			}

			String key = bdmMonitDevice.getDeviceType() + "-" + bdmMonitDevice.getId();
			Map<String, Object> innerMap = new HashMap<>();
			innerMap.put("uniqueId", bdmMonitDevice.getUniqueId());
			innerMap.put("deviceNum", bdmMonitDevice.getDeviceNum());
			innerMap.put("category", bdmMonitDevice.getCategory());
			innerMap.put("deviceType", bdmMonitDevice.getDeviceType());
			innerMap.put("deptId", bdmMonitDevice.getDeptId());
			innerMap.put("iotProtocol", bdmMonitDevice.getIotProtocol());
			try {
				map.put(key, new ObjectMapper().writeValueAsString(innerMap));
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			// 待分配终端对象 缓存到 baseinfo_target
			if (bdmMonitDevice.getSpecificity().equals(BdmTypeEnum.NEW.getValue())) {
				String targetKey = TargetTypeEnum.ENDPOINTS.getSymbol() + "-" + bdmAbstractTarget.getId();
				Map<String, Object> targetInnerMap = new HashMap<>();
				targetInnerMap.put("targetName", bdmAbstractTarget.getNumber());
				targetInnerMap.put("targetType", bdmAbstractTarget.getTargetType());
				targetInnerMap.put("deptId", bdmAbstractTarget.getDeptId());
				try {
					targrtMap.put(targetKey, new ObjectMapper().writeValueAsString(targetInnerMap));
				} catch (JsonProcessingException e) {
					e.printStackTrace();
				}
			}
		}
		if (!virtualTargets.isEmpty()) {
			MapperUtils.splitListByCapacity(virtualTargets, MapperUtils.DEFAULT_CAPACITY)
				.forEach(virtualTargetList -> this.bdmVirtualTargetService.insertBatch(virtualTargetList));
		}
		if (!abstractDevices.isEmpty()) {
			MapperUtils.splitListByCapacity(abstractDevices, MapperUtils.DEFAULT_CAPACITY)
				.forEach(abstractDeviceList -> this.bdmAbstractDeviceService.insertBatch(abstractDeviceList));
		}
		// 新设备同步到抽象监控对象实体表
		if (!abstractTargets.isEmpty()) {
			MapperUtils.splitListByCapacity(abstractTargets, MapperUtils.DEFAULT_CAPACITY)
				.forEach(abstractTargetList -> this.bdmAbstractTargetService.insertBatch(abstractTargetList));
		}

		if (!map.isEmpty()) {
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);
		}
		if (!targrtMap.isEmpty()) {
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, targrtMap);
		}
		if (!terminalIotRequests.isEmpty()) {
			this.iotCardService.updateDeviceId(terminalIotRequests);
		}

		// 查询 deviceNum 列表
		List<String> deviceNumList = filteredList.stream()
			.filter(request -> BdmTypeEnum.NEW.getValue().equals(request.getSpecificity()))
			.map(MonitDeviceRequest::getDeviceNum)
			.collect(Collectors.toList());

		// 查询 uniqueId 列表
		List<String> uniqueIdList = filteredList.stream()
			.filter(request -> BdmTypeEnum.NEW.getValue().equals(request.getSpecificity()))
			.map(MonitDeviceRequest::getUniqueId)
			.collect(Collectors.toList());

		if (!uniqueIdList.isEmpty()) {
			//跟新bdm_terminal
			terminalService.updateFormalByDeviceSeqs(uniqueIdList);
		}
		if (!deviceNumList.isEmpty()) {
			//更新bdm_device_code
			deviceCodeService.updateActivatedByDeviceNum(deviceNumList);
		}
		return arrayList;
	}

	/**
	 * 发送消息
	 */
	private void sendNotifications(List<MonitDeviceRequest> filteredList, List<Long> arrayList) {
		MonitDeviceRequest request = filteredList.get(filteredList.size() - 1);
		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.IMPORT);
		deviceInfo.setDeviceId(request.getId());
		deviceInfo.setDeviceType(request.getDeviceType());
		deviceInfo.setDeviceModel(request.getModel());
		deviceInfo.setDeviceNum(request.getDeviceNum());
		deviceInfo.setDeviceUniqueId(request.getUniqueId());
		deviceInfo.setDeptId(request.getDeptId());
		Set<Long> ids = filteredList.stream().map(MonitDeviceRequest::getId).collect(Collectors.toSet());
		deviceInfo.setDeviceIds(ids);
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_monit_device", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("监测终端信息更新消息发送到kafka失败", e);
		}
		QueryWrapper<BdmMonitDevice> wrapper = new QueryWrapper<>();
		if (!arrayList.isEmpty()) {
			MybatisPlusWrapperUitls.longListIn(wrapper, "id", arrayList);
			List<BdmMonitDevice> monitDevices = baseMapper.selectList(wrapper);
			//messageClient
			List<Object> terminals = new ArrayList<>(monitDevices);
			try {
				messageClient.terminalBatch(CommonConstant.OPER_TYPE_ADD, BaseInfoConstants.MONIT_DEVICE_TYPE, terminals);
			} catch (Exception e) {
				log.error("消息发送到messageClient失败：" + e.getMessage());
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateByDeviceId(Long id, Integer targetType) {
		this.monitDeviceMapper.updateByDeviceId(id, targetType);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateBatchByTerminalId(List<FacilityTerminalRequest> monits, Long id, Integer targetType, Long deptId) {
		Map<String, String> map = new HashMap<>();
		for (FacilityTerminalRequest request : monits) {
			this.monitDeviceMapper.updateBatchByTerminalId(request, id, targetType, deptId);

			String key = request.getDeviceType() + "-" + request.getId();
			Map<String, Object> innerMap = new HashMap<>();
			innerMap.put("uniqueId", request.getUniqueId());
			innerMap.put("deviceNum", request.getDeviceNum());
			innerMap.put("category", request.getCategory());
			innerMap.put("deviceType", request.getDeviceType());
			innerMap.put("deptId", request.getDeptId());
			innerMap.put("iotProtocol", request.getIotProtocol());
			try {
				map.put(key, new ObjectMapper().writeValueAsString(innerMap));
			} catch (JsonProcessingException e) {
				log.error(e.getMessage());
			}
		}
		if (!map.isEmpty()) {
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void deleteByTargetIds(Long[] ids) {
		this.monitDeviceMapper.deleteByTargetIds(ids);
	}

	@Override
	public long countByUserRole(DataAuthCE ceDataAuth) {
		AuthInfo response = deptProcessingUtil.handle(ceDataAuth);
		return baseMapper.countByUserRole(response.getAccount(), response.getOrgList());
	}

	@Override
	public void updateDept(Long id, Integer targetType, Long deptId) {
		List<BdmMonitDevice> monitDevices = baseMapper.selectList(
			new QueryWrapper<BdmMonitDevice>()
				.eq("target_id", id)
				.eq("target_type", targetType)
				.eq("deleted", 0)
				.select("id,unique_id,device_type,dept_id,category,device_num,iot_protocol")
		);
		if (!monitDevices.isEmpty()) {
			baseMapper.updateDept(id, targetType, deptId);

			Map<String, String> map = new HashMap<>();
			for (BdmMonitDevice monitDevice : monitDevices) {
				String key = monitDevice.getDeviceType() + "-" + monitDevice.getId();
				Map<String, Object> innerMap = new HashMap<>();
				innerMap.put("uniqueId", monitDevice.getUniqueId());
				innerMap.put("deviceNum", monitDevice.getDeviceNum());
				innerMap.put("category", monitDevice.getCategory());
				innerMap.put("deviceType", monitDevice.getDeviceType());
				innerMap.put("deptId", deptId);
				innerMap.put("iotProtocol", monitDevice.getIotProtocol());
				try {
					map.put(key, new ObjectMapper().writeValueAsString(innerMap));
				} catch (JsonProcessingException e) {
					e.printStackTrace();
				}
			}
			if (!map.isEmpty()) {
				redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);
			}
		}
	}

	/**
	 * 对象字段初始化
	 *
	 * @param request
	 * @return
	 */
	public BdmMonitDevice init(MonitDeviceRequest request) {
		BdmMonitDevice monitDevice = new BdmMonitDevice();
		monitDevice.setId(request.getId());
		monitDevice.setUniqueId(request.getUniqueId() != null ? request.getUniqueId() : "");
		monitDevice.setImei(request.getImei() != null ? request.getImei() : "");
		monitDevice.setModel(request.getModel() != null ? request.getModel() : "");
		monitDevice.setVendor(request.getVendor() != null ? request.getVendor() : "");
		monitDevice.setBdChipSn(request.getBdChipSn() != null ? request.getBdChipSn() : "");
		monitDevice.setDeviceType(DeviceTypeEnum.MONIT.getSymbol());
		monitDevice.setSpecificity(request.getSpecificity() != null ? request.getSpecificity() : 0);
		monitDevice.setDeptId(request.getDeptId() != null ? request.getDeptId() : 0);
		monitDevice.setCategory(request.getCategory() != null ? request.getCategory() : 0);
		monitDevice.setDeviceNum(request.getDeviceNum() != null ? request.getDeviceNum() : "");
		monitDevice.setInstalldate(request.getInstalldate() != null ? request.getInstalldate() : null);
		monitDevice.setCreateTime(request.getCreateTime() != null ? request.getCreateTime() : null);
		monitDevice.setUpdateTime(request.getUpdateTime() != null ? request.getUpdateTime() : new Date());
		monitDevice.setDeleted(request.getDeleted() != null ? request.getDeleted() : 0);
		monitDevice.setMonitoringSrc(request.getMonitoringSrc() != null ? request.getMonitoringSrc() : "");
		monitDevice.setScenario(request.getScenario() != null ? request.getScenario() : 0);
		monitDevice.setDomain(request.getDomain() != null ? request.getDomain() : 0);
		monitDevice.setGnssMode(request.getGnssMode() != null ? request.getGnssMode() : 0);
		monitDevice.setIotProtocol(request.getIotProtocol() != null ? request.getIotProtocol() : IotProtocolEnum.JT808.getCode());
		monitDevice.setTerminalId(request.getTerminalId() != null ? request.getTerminalId() : "");
		monitDevice.setAssetType(request.getAssetType() != null ? request.getAssetType() : 0);
		monitDevice.setOwnDeptType(request.getOwnDeptType() != null ? request.getOwnDeptType() : 0);
		monitDevice.setOwnDeptName(request.getOwnDeptName() != null ? request.getOwnDeptName() : "");
		return monitDevice;
	}

	@Override
	public int bindTarget(Long deviceId, Long targetId, Integer targetType,String targetName){
		return this.monitDeviceMapper.bindTarget(deviceId,targetId, targetType,targetName);
	}
}
