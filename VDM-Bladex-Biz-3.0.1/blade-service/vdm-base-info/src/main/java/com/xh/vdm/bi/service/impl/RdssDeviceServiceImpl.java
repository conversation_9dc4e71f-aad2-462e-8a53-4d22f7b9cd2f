package com.xh.vdm.bi.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xh.vdm.bi.constant.BaseInfoConstants;
import com.xh.vdm.bi.enums.*;
import com.xh.vdm.bi.mapper.RdssDeviceMapper;
import com.xh.vdm.bi.service.*;
import com.xh.vdm.bi.service.bdCheck.IBdcTerminalService;
import com.xh.vdm.bi.utils.DeviceNumUtils;
import com.xh.vdm.bi.utils.MapperUtils;
import com.xh.vdm.bi.utils.SnowflakeIdWorker;
import com.xh.vdm.bi.utils.TargetSnowflakeIdWorker;
import com.xh.vdm.bi.vo.request.DeviceNoBindRequest;
import com.xh.vdm.bi.vo.request.PersonTerminalRequest;
import com.xh.vdm.bi.vo.request.RdssDeviceRequest;
import com.xh.vdm.bi.vo.request.TerminalIotRequest;
import com.xh.vdm.bi.vo.response.DeviceInfo;
import com.xh.vdm.bi.vo.response.PersonNoBingResponse;
import com.xh.vdm.bi.vo.response.RdssDeviceResponse;
import com.xh.vdm.bi.vo.response.WorkerBingResponse;
import com.xh.vdm.biapi.entity.*;
import com.xh.vdm.interManager.feign.IMessageClient;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.Constants;
import org.springblade.common.enums.Operation;
import org.springblade.common.utils.CETokenUtil;
import org.springblade.common.utils.DeptProcessingUtil;
import org.springblade.common.utils.MybatisPlusWrapperUitls;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.entity.AuthInfo;
import org.springblade.entity.DataAuthCE;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 短报文终端管理
 */
@Service
@Slf4j
public class RdssDeviceServiceImpl extends ServiceImpl<RdssDeviceMapper, BdmRdssDevice> implements RdssDeviceService {
	@Resource
	private RdssDeviceMapper rdssDeviceMapper;
	@Resource
	private IotCardService iotCardService;

	@Resource
	private CETokenUtil ceTokenUtil;
	@Resource
	private RedisTemplate redisTemplate;
	@Autowired
	private DeviceNumUtils deviceNumUtils;
	@Resource
	private IBdcTerminalService terminalService;
	@Resource
	private BdmDeviceCodeService deviceCodeService;
	@Resource
	private KafkaTemplate<String, String> kafkaTemplate;
	@Resource
	private BdmDeviceLedgerService deviceLedgerService;
	@Resource
	private IMessageClient messageClient;
	@Value("${current.schema}")
	String schema;
	@Resource
	private IBdmVirtualTargetService bdmVirtualTargetService;
	@Resource
	private IBdmAbstractDeviceService bdmAbstractDeviceService;
	@Resource
	private IBdmAbstractTargetService bdmAbstractTargetService;
	@Resource
	private DeptProcessingUtil deptProcessingUtil;

	/**
	 * 分页查询
	 *
	 * @param rdssDeviceRequest 筛选条件
	 * @param ceDataAuth
	 * @return 查询结果
	 */
	@Override
	public IPage<RdssDeviceResponse> queryByPage(RdssDeviceRequest rdssDeviceRequest, DataAuthCE ceDataAuth) {
		Page page = new Page();
		page.setCurrent(rdssDeviceRequest.getCurrent());
		page.setSize(rdssDeviceRequest.getSize());
		AuthInfo response = deptProcessingUtil.handle(ceDataAuth);
		return this.rdssDeviceMapper.queryAll(page, rdssDeviceRequest, response.getAccount(), response.getOrgList(), schema);
	}

	/**
	 * 通过ID查询单条数据
	 *
	 * @param id 主键
	 * @return 实例对象
	 */
	@Override
	public RdssDeviceResponse queryById(Long id) {
		return this.rdssDeviceMapper.queryById(id);
	}


	/**
	 * 新增数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public BdmRdssDevice insert(RdssDeviceRequest request) {
		BdmRdssDevice bdmRdssDevice = new BdmRdssDevice();
		QueryWrapper<BdmRdssDevice> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("unique_id", request.getUniqueId());
		BdmRdssDevice rdss = baseMapper.selectOne(queryWrapper);
		TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(BaseInfoConstants.VIRTUAL_TARGET_TYPE, BaseInfoConstants.ZERO, BaseInfoConstants.ZERO);
		if (rdss != null) {
			request.setId(rdss.getId());
			if (request.getSpecificity().equals(BdmTypeEnum.OLD.getValue())) {
				R<String> r1 = deviceNumUtils.deviceNumProducer(ProductTypeEnum.O.name());
				if (r1.isSuccess()) {
					request.setDeviceNum(r1.getData());
				}
			}
			if (BdmTypeEnum.SPECIFICITY.getValue().equals(request.getSpecificity())) {
				R<String> r1 = deviceNumUtils.deviceNumProducer(ProductTypeEnum.S.name());
				if (r1.isSuccess()) {
					request.setDeviceNum(r1.getData());
				}
			}
			if (BdmTypeEnum.NEW.getValue().equals(request.getSpecificity())) {
				request.setGnssMode(GnssModeEnum.SINGLEBEIDOU.getValue());
			}
			request.setCreateTime(new Date());
			request.setDeleted(0);
			//request.setDeviceNum(rdss.getDeviceNum());
			bdmRdssDevice = this.init(request);
			//同步到待分配终端对象实体表
			QueryWrapper<BdmVirtualTarget> wrapper = new QueryWrapper<>();
			wrapper.eq("number", bdmRdssDevice.getUniqueId());
			wrapper.last("LIMIT 1");
			BdmVirtualTarget latestRecord = bdmVirtualTargetService.getOne(wrapper);
			BdmVirtualTarget virtualTarget = new BdmVirtualTarget();
			BeanUtils.copyProperties(bdmRdssDevice, virtualTarget, "id");
			virtualTarget.setTargetType(0);
			virtualTarget.setDeviceCode(bdmRdssDevice.getDeviceNum());
			virtualTarget.setNumber(bdmRdssDevice.getUniqueId());
			if (latestRecord == null) {
				virtualTarget.setId(targetId.nextId());
				virtualTarget.setCreateAccount(AuthUtil.getUserAccount());
				boolean saveRes = bdmVirtualTargetService.save(virtualTarget);
				if (saveRes) {
					log.info("bdmVirtualTargetService漏掉的 新增成功");
				} else {
					log.info("bdmVirtualTargetService漏掉的 新增失败:{}", virtualTarget);
				}
			} else {
				virtualTarget.setId(latestRecord.getId());
				boolean updateRes = bdmVirtualTargetService.updateById(virtualTarget);
				if (updateRes) {
					log.info("bdmVirtualTargetService 更新成功");
				} else {
					log.info("bdmVirtualTargetService 更新失败:{}", virtualTarget);
				}
			}
			// TODO redis 缓存到 baseinfo_target

			//同步到抽象监控对象表
			QueryWrapper<BdmAbstractTarget> batWrapper = new QueryWrapper<>();
			batWrapper.eq("number", bdmRdssDevice.getUniqueId());
			batWrapper.last("LIMIT 1");
			BdmAbstractTarget batLatestRecord = bdmAbstractTargetService.getOne(batWrapper);
			// 同步到抽象监控对象表
			BdmAbstractTarget bdmAbstractTarget = new BdmAbstractTarget();
			bdmAbstractTarget.setId(virtualTarget.getId());
			bdmAbstractTarget.setNumber(bdmRdssDevice.getUniqueId());
			bdmAbstractTarget.setName(bdmRdssDevice.getUniqueId());
			bdmAbstractTarget.setTargetType(0);
			bdmAbstractTarget.setDeptId(bdmRdssDevice.getDeptId());
			bdmAbstractTarget.setCategory(0);
			bdmAbstractTarget.setDeleted(0);
			if (batLatestRecord == null) {
				boolean saveRes = bdmAbstractTargetService.save(bdmAbstractTarget);
				if (saveRes) {
					log.info("bdmAbstractTargetService漏掉的 新增成功");
				} else {
					log.info("bdmAbstractTargetService漏掉的 新增失败:{}", bdmAbstractTarget);
				}
			} else {
				boolean updateRes = bdmAbstractTargetService.updateById(bdmAbstractTarget);
				if (updateRes) {
					log.info("bdmAbstractTargetService 更新成功");
				} else {
					log.info("bdmAbstractTargetService 更新失败:{}", bdmAbstractTarget);
				}
			}
			bdmRdssDevice.setTargetId(virtualTarget.getId());
			baseMapper.update(bdmRdssDevice);
			//同步到抽象监控对象实体表
			BdmAbstractDevice abstractDevice = new BdmAbstractDevice();
			BeanUtils.copyProperties(bdmRdssDevice, abstractDevice);
			abstractDevice.setTargetType(0);
			abstractDevice.setDeleted(0);
			boolean updateRes=bdmAbstractDeviceService.updateById(abstractDevice);
			if (updateRes){
				log.info("bdmAbstractDeviceService 更新成功");
			}else {
				log.info("bdmAbstractDeviceService 更新失败:{}",abstractDevice);
			}
		} else {
			if (request.getSpecificity().equals(BdmTypeEnum.OLD.getValue())) {
				R<String> r1 = deviceNumUtils.deviceNumProducer(ProductTypeEnum.O.name());
				if (r1.isSuccess()) {
					request.setDeviceNum(r1.getData());
				}
			}
			if (BdmTypeEnum.SPECIFICITY.getValue().equals(request.getSpecificity())) {
				R<String> r1 = deviceNumUtils.deviceNumProducer(ProductTypeEnum.S.name());
				if (r1.isSuccess()) {
					request.setDeviceNum(r1.getData());
				}
			}
			SnowflakeIdWorker idWorker = new SnowflakeIdWorker(BaseInfoConstants.RDSS_DEVICE_TYPE, BaseInfoConstants.ZERO, BaseInfoConstants.ZERO);
			request.setId(idWorker.nextId());
			if (BdmTypeEnum.NEW.getValue().equals(request.getSpecificity())) {
				request.setGnssMode(GnssModeEnum.SINGLEBEIDOU.getValue());
			}
			request.setCreateTime(new Date());
			bdmRdssDevice = this.init(request);
			String account = AuthUtil.getUserAccount();
			bdmRdssDevice.setCreateAccount(account);
			//同步到待分配终端对象实体表
			BdmVirtualTarget virtualTarget = new BdmVirtualTarget();
			BeanUtils.copyProperties(bdmRdssDevice, virtualTarget);
			virtualTarget.setId(targetId.nextId());
			virtualTarget.setTargetType(0);
			virtualTarget.setDeviceCode(bdmRdssDevice.getDeviceNum());
			virtualTarget.setNumber(bdmRdssDevice.getUniqueId());
			virtualTarget.setCreateAccount(account);
			bdmVirtualTargetService.save(virtualTarget);

			//同步到抽象监控对象表
			BdmAbstractTarget bdmAbstractTarget = new BdmAbstractTarget();
			bdmAbstractTarget.setId(virtualTarget.getId());
			bdmAbstractTarget.setNumber(bdmRdssDevice.getUniqueId());
			bdmAbstractTarget.setName(bdmRdssDevice.getUniqueId());
			bdmAbstractTarget.setTargetType(0);
			bdmAbstractTarget.setDeptId(bdmRdssDevice.getDeptId());
			bdmAbstractTarget.setCategory(0);
			bdmAbstractTarget.setDeleted(0);
			bdmAbstractTarget.setCreateAccount(account);
			bdmAbstractTargetService.save(bdmAbstractTarget);
			//}
			bdmRdssDevice.setTargetId(virtualTarget.getId());
			this.rdssDeviceMapper.insertRdss(bdmRdssDevice);
			//同步到抽象监控对象实体表
			BdmAbstractDevice abstractDevice = new BdmAbstractDevice();
			BeanUtils.copyProperties(bdmRdssDevice, abstractDevice);
			abstractDevice.setTargetType(0);
			abstractDevice.setDeleted(0);
			abstractDevice.setCreateAccount(account);
			bdmAbstractDeviceService.saveDevice(abstractDevice);

		}

		if (request.getNumbers() != null && !request.getNumbers().isEmpty()) {
			//更新存在且未绑定的物联网卡信息
			iotCardService.updateIotCardByDeviceId(request.getNumbers(), bdmRdssDevice.getId(), bdmRdssDevice.getDeviceType());
		}

		if (bdmRdssDevice.getSpecificity().equals(BdmTypeEnum.NEW.getValue())) {
			//跟新bdm_terminal
			terminalService.updateFormalByDeviceSeq(request.getUniqueId());
			//更新bdm_device_code
			deviceCodeService.updateActivated(request.getDeviceNum());
		}

		BdmRdssDevice rdssDevice = getBaseMapper().selectById(bdmRdssDevice.getId());

		Map<String, String> map = new HashMap<>();
		String key = rdssDevice.getDeviceType() + "-" + rdssDevice.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("uniqueId", rdssDevice.getUniqueId());
		innerMap.put("deviceNum", rdssDevice.getDeviceNum());
		innerMap.put("category", rdssDevice.getCategory());
		innerMap.put("deviceType", rdssDevice.getDeviceType());
		innerMap.put("deptId", rdssDevice.getDeptId());
		innerMap.put("iotProtocol", rdssDevice.getIotProtocol());
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		//todo：Map<String, String> map这样写go那边解析有问题
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);

		if (BdmTypeEnum.NEW.getValue().equals(request.getSpecificity())) {
			Map<String, String> targetMap = new HashMap<>();
			String targetKey = TargetTypeEnum.ENDPOINTS.getSymbol() + "-" + rdssDevice.getId();
			Map<String, Object> targetInnerMap = new HashMap<>();
			targetInnerMap.put("targetName", rdssDevice.getUniqueId());
			targetInnerMap.put("targetType", 0);
			targetInnerMap.put("deptId", rdssDevice.getDeptId());
			try {
				map.put(targetKey, new ObjectMapper().writeValueAsString(targetInnerMap));
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			//todo：这样写go那边解析有问题
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, targetMap);
		}

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.INSERT);
		deviceInfo.setDeviceId(rdssDevice.getId());
		deviceInfo.setDeviceType(rdssDevice.getDeviceType());
		deviceInfo.setDeviceUniqueId(rdssDevice.getUniqueId());
		deviceInfo.setDeviceModel(rdssDevice.getModel());
		deviceInfo.setDeviceNum(rdssDevice.getDeviceNum());
		deviceInfo.setDeptId(rdssDevice.getDeptId());
		deviceInfo.setSpecificity(rdssDevice.getSpecificity());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_rdss_device", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("短报文终端信息更新消息发送到kafka失败", e);
		}
		//messageClient
		try {
			messageClient.terminal(CommonConstant.OPER_TYPE_ADD, rdssDevice.getDeviceType(), rdssDevice);
		} catch (Exception e) {
			log.error("消息发送到messageClient失败：" + e.getMessage());
		}

		return rdssDevice;
	}

	@Override
	public BdmRdssDevice insertRdssDevice(RdssDeviceRequest request) {
		BdmRdssDevice bdmRdssDevice = new BdmRdssDevice();
		QueryWrapper<BdmRdssDevice> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("unique_id", request.getUniqueId());
		BdmRdssDevice rdss = baseMapper.selectOne(queryWrapper);
		TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(BaseInfoConstants.VIRTUAL_TARGET_TYPE, BaseInfoConstants.ZERO, BaseInfoConstants.ZERO);
		if (rdss != null) {
			request.setId(rdss.getId());
			request.setCreateTime(new Date());
			request.setDeleted(0);
			bdmRdssDevice = this.init(request);
			//同步到待分配终端对象实体表
			QueryWrapper<BdmVirtualTarget> wrapper = new QueryWrapper<>();
			wrapper.eq("number", bdmRdssDevice.getUniqueId());
			wrapper.last("LIMIT 1");
			BdmVirtualTarget latestRecord = bdmVirtualTargetService.getOne(wrapper);
			BdmVirtualTarget virtualTarget = new BdmVirtualTarget();
			BeanUtils.copyProperties(bdmRdssDevice, virtualTarget, "id");
			virtualTarget.setTargetType(0);
			virtualTarget.setDeviceCode(bdmRdssDevice.getDeviceNum());
			virtualTarget.setNumber(bdmRdssDevice.getUniqueId());
			if (latestRecord == null) {
				virtualTarget.setId(targetId.nextId());
				virtualTarget.setCreateAccount(AuthUtil.getUserAccount());
				boolean saveRes = bdmVirtualTargetService.save(virtualTarget);
				if (saveRes) {
					log.info("bdmVirtualTargetService漏掉的 新增成功");
				} else {
					log.info("bdmVirtualTargetService漏掉的 新增失败:{}", virtualTarget);
				}
			} else {
				virtualTarget.setId(latestRecord.getId());
				boolean updateRes = bdmVirtualTargetService.updateById(virtualTarget);
				if (updateRes) {
					log.info("bdmVirtualTargetService 更新成功");
				} else {
					log.info("bdmVirtualTargetService 更新失败:{}", virtualTarget);
				}
			}
			//同步到抽象监控对象表
			QueryWrapper<BdmAbstractTarget> batWrapper = new QueryWrapper<>();
			batWrapper.eq("number", bdmRdssDevice.getUniqueId());
			batWrapper.last("LIMIT 1");
			BdmAbstractTarget batLatestRecord = bdmAbstractTargetService.getOne(batWrapper);
			// 同步到抽象监控对象表
			BdmAbstractTarget bdmAbstractTarget = new BdmAbstractTarget();
			bdmAbstractTarget.setId(virtualTarget.getId());
			bdmAbstractTarget.setNumber(bdmRdssDevice.getUniqueId());
			bdmAbstractTarget.setName(bdmRdssDevice.getUniqueId());
			bdmAbstractTarget.setTargetType(0);
			bdmAbstractTarget.setDeptId(bdmRdssDevice.getDeptId());
			bdmAbstractTarget.setCategory(0);
			bdmAbstractTarget.setDeleted(0);
			if (batLatestRecord == null) {
				boolean saveRes = bdmAbstractTargetService.save(bdmAbstractTarget);
				if (saveRes) {
					log.info("bdmAbstractTargetService漏掉的 新增成功");
				} else {
					log.info("bdmAbstractTargetService漏掉的 新增失败:{}", bdmAbstractTarget);
				}
			} else {
				boolean updateRes = bdmAbstractTargetService.updateById(bdmAbstractTarget);
				if (updateRes) {
					log.info("bdmAbstractTargetService 更新成功");
				} else {
					log.info("bdmAbstractTargetService 更新失败:{}", bdmAbstractTarget);
				}
			}
			bdmRdssDevice.setTargetId(virtualTarget.getId());
			baseMapper.update(bdmRdssDevice);
			//同步到抽象监控对象实体表
		} else {
			SnowflakeIdWorker idWorker = new SnowflakeIdWorker(BaseInfoConstants.RDSS_DEVICE_TYPE, BaseInfoConstants.ZERO, BaseInfoConstants.ZERO);
			request.setId(idWorker.nextId());
			if (BdmTypeEnum.NEW.getValue().equals(request.getSpecificity())) {
				request.setGnssMode(GnssModeEnum.SINGLEBEIDOU.getValue());
			}
			request.setCreateTime(new Date());
			bdmRdssDevice = this.init(request);
			String account = AuthUtil.getUserAccount();
			bdmRdssDevice.setCreateAccount(account);
			//同步到待分配终端对象实体表
			BdmVirtualTarget virtualTarget = new BdmVirtualTarget();
			BeanUtils.copyProperties(bdmRdssDevice, virtualTarget);
			virtualTarget.setId(null);
			virtualTarget.setTargetType(0);
			virtualTarget.setDeviceCode(bdmRdssDevice.getDeviceNum());
			virtualTarget.setNumber(bdmRdssDevice.getUniqueId());
			virtualTarget.setCreateAccount(account);
			bdmVirtualTargetService.save(virtualTarget);
			//同步到抽象监控对象表
			BdmAbstractTarget bdmAbstractTarget = new BdmAbstractTarget();
			bdmAbstractTarget.setId(virtualTarget.getId());
			bdmAbstractTarget.setNumber(bdmRdssDevice.getUniqueId());
			bdmAbstractTarget.setName(bdmRdssDevice.getUniqueId());
			bdmAbstractTarget.setTargetType(0);
			bdmAbstractTarget.setDeptId(bdmRdssDevice.getDeptId());
			bdmAbstractTarget.setCategory(0);
			bdmAbstractTarget.setDeleted(0);
			bdmAbstractTarget.setCreateAccount(account);
			bdmAbstractTargetService.save(bdmAbstractTarget);
			//}
			bdmRdssDevice.setTargetId(virtualTarget.getId());
			bdmRdssDevice.setId(null);
			this.rdssDeviceMapper.insert(bdmRdssDevice);
		}

		if (request.getNumbers() != null && !request.getNumbers().isEmpty()) {
			//更新存在且未绑定的物联网卡信息
			iotCardService.updateIotCardByDeviceId(request.getNumbers(), bdmRdssDevice.getId(), bdmRdssDevice.getDeviceType());
		}

		if (bdmRdssDevice.getSpecificity().equals(BdmTypeEnum.NEW.getValue())) {
			//跟新bdm_terminal
			terminalService.updateFormalByDeviceSeq(request.getUniqueId());
			//更新bdm_device_code
			deviceCodeService.updateActivated(request.getDeviceNum());
		}

		BdmRdssDevice rdssDevice = getBaseMapper().selectById(bdmRdssDevice.getId());

		Map<String, String> map = new HashMap<>();
		String key = rdssDevice.getDeviceType() + "-" + rdssDevice.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("uniqueId", rdssDevice.getUniqueId());
		innerMap.put("deviceNum", rdssDevice.getDeviceNum());
		innerMap.put("category", rdssDevice.getCategory());
		innerMap.put("deviceType", rdssDevice.getDeviceType());
		innerMap.put("deptId", rdssDevice.getDeptId());
		innerMap.put("iotProtocol", rdssDevice.getIotProtocol());
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		//todo：Map<String, String> map这样写go那边解析有问题
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);

		if (BdmTypeEnum.NEW.getValue().equals(request.getSpecificity())) {
			Map<String, String> targetMap = new HashMap<>();
			String targetKey = TargetTypeEnum.ENDPOINTS.getSymbol() + "-" + rdssDevice.getId();
			Map<String, Object> targetInnerMap = new HashMap<>();
			targetInnerMap.put("targetName", rdssDevice.getUniqueId());
			targetInnerMap.put("targetType", 0);
			targetInnerMap.put("deptId", rdssDevice.getDeptId());
			try {
				map.put(targetKey, new ObjectMapper().writeValueAsString(targetInnerMap));
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			//todo：这样写go那边解析有问题
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, targetMap);
		}

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.INSERT);
		deviceInfo.setDeviceId(rdssDevice.getId());
		deviceInfo.setDeviceType(rdssDevice.getDeviceType());
		deviceInfo.setDeviceUniqueId(rdssDevice.getUniqueId());
		deviceInfo.setDeviceModel(rdssDevice.getModel());
		deviceInfo.setDeviceNum(rdssDevice.getDeviceNum());
		deviceInfo.setDeptId(rdssDevice.getDeptId());
		deviceInfo.setSpecificity(rdssDevice.getSpecificity());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_rdss_device", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("短报文终端信息更新消息发送到kafka失败", e);
		}
		//messageClient
		try {
			messageClient.terminal(CommonConstant.OPER_TYPE_ADD, rdssDevice.getDeviceType(), rdssDevice);
		} catch (Exception e) {
			log.error("消息发送到messageClient失败：" + e.getMessage());
		}

		return rdssDevice;
	}

	/**
	 * 修改数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public BdmRdssDevice update(RdssDeviceRequest request) {
		BdmRdssDevice bdmRdssDevice = this.init(request);
		this.rdssDeviceMapper.update(bdmRdssDevice);

		iotCardService.updateDeviceIdById(request.getId(), request.getDeviceType(), request.getNumbers());

		if (request.getNumbers() != null && !request.getNumbers().isEmpty()) {
			iotCardService.updateIotCardByDeviceId(request.getNumbers(), request.getId(), request.getDeviceType());
		}

		BdmRdssDevice rdssDevice = getBaseMapper().selectById(bdmRdssDevice.getId());

		// 未绑定的非新设备更新抽象终端表
		if (rdssDevice.getSpecificity() != BdmTypeEnum.NEW.getValue()) {
			BdmAbstractDevice abstractDevice = new BdmAbstractDevice();
			BeanUtils.copyProperties(rdssDevice, abstractDevice);
			bdmAbstractDeviceService.updateById(abstractDevice);
		}

		// 更新Redis缓存
		String key = rdssDevice.getDeviceType() + "-" + rdssDevice.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("uniqueId", rdssDevice.getUniqueId());
		innerMap.put("deviceNum", rdssDevice.getDeviceNum());
		innerMap.put("category", rdssDevice.getCategory());
		innerMap.put("deviceType", rdssDevice.getDeviceType());
		innerMap.put("deptId", rdssDevice.getDeptId());
		innerMap.put("iotProtocol", rdssDevice.getIotProtocol());
		Map<String, String> map = new HashMap<>();
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		//todo：Map<String, String> map这样写go那边解析有问题
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.UPDATE);
		deviceInfo.setDeviceId(rdssDevice.getId());
		deviceInfo.setDeviceType(rdssDevice.getDeviceType());
		deviceInfo.setDeviceUniqueId(rdssDevice.getUniqueId());
		deviceInfo.setDeviceModel(rdssDevice.getModel());
		deviceInfo.setDeviceNum(rdssDevice.getDeviceNum());
		deviceInfo.setDeptId(rdssDevice.getDeptId());
		deviceInfo.setSpecificity(rdssDevice.getSpecificity());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_rdss_device", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("短报文终端信息更新消息发送到kafka失败", e);
		}
		//messageClient
		try {
			messageClient.terminal(CommonConstant.OPER_TYPE_UPDATE, rdssDevice.getDeviceType(), rdssDevice);
		} catch (Exception e) {
			log.error("消息发送到messageClient失败：" + e.getMessage());
		}
		return rdssDevice;
	}

	@Override
	public BdmRdssDevice updateRdssDevice(RdssDeviceRequest request) {
		BdmRdssDevice bdmRdssDevice = this.init(request);
		this.rdssDeviceMapper.update(bdmRdssDevice);

		iotCardService.updateDeviceIdById(request.getId(), request.getDeviceType(), request.getNumbers());

		if (request.getNumbers() != null && !request.getNumbers().isEmpty()) {
			iotCardService.updateIotCardByDeviceId(request.getNumbers(), request.getId(), request.getDeviceType());
		}

		BdmRdssDevice rdssDevice = getBaseMapper().selectById(bdmRdssDevice.getId());

		// 更新Redis缓存
		String key = rdssDevice.getDeviceType() + "-" + rdssDevice.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("uniqueId", rdssDevice.getUniqueId());
		innerMap.put("deviceNum", rdssDevice.getDeviceNum());
		innerMap.put("category", rdssDevice.getCategory());
		innerMap.put("deviceType", rdssDevice.getDeviceType());
		innerMap.put("deptId", rdssDevice.getDeptId());
		innerMap.put("iotProtocol", rdssDevice.getIotProtocol());
		Map<String, String> map = new HashMap<>();
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		//todo：Map<String, String> map这样写go那边解析有问题
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.UPDATE);
		deviceInfo.setDeviceId(rdssDevice.getId());
		deviceInfo.setDeviceType(rdssDevice.getDeviceType());
		deviceInfo.setDeviceUniqueId(rdssDevice.getUniqueId());
		deviceInfo.setDeviceModel(rdssDevice.getModel());
		deviceInfo.setDeviceNum(rdssDevice.getDeviceNum());
		deviceInfo.setDeptId(rdssDevice.getDeptId());
		deviceInfo.setSpecificity(rdssDevice.getSpecificity());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_rdss_device", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("短报文终端信息更新消息发送到kafka失败", e);
		}
		//messageClient
		try {
			messageClient.terminal(CommonConstant.OPER_TYPE_UPDATE, rdssDevice.getDeviceType(), rdssDevice);
		} catch (Exception e) {
			log.error("消息发送到messageClient失败：" + e.getMessage());
		}
		return rdssDevice;
	}

	/**
	 * 通过主键删除数据
	 *
	 * @param id 主键
	 * @return 是否成功
	 */
	@Override
	public boolean deleteById(Long id) {
		return this.rdssDeviceMapper.deleteById(id) > 0;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean deleteByIds(Long[] ids) {

		QueryWrapper<BdmRdssDevice> wrapper = new QueryWrapper<>();
		wrapper.in("id", ids);
		List<BdmRdssDevice> list = this.rdssDeviceMapper.selectList(wrapper);

		List<String> deviceNumList = new ArrayList<>();
		List<String> uniqueIdList = new ArrayList<>();

		if (!list.isEmpty()) {
			// 查询 deviceNum 列表
			deviceNumList = list.stream()
				.filter(request -> BdmTypeEnum.NEW.getValue().equals(request.getSpecificity()))
				.map(BdmRdssDevice::getDeviceNum)
				.collect(Collectors.toList());

			// 查询 uniqueId 列表
			uniqueIdList = list.stream()
				.filter(request -> BdmTypeEnum.NEW.getValue().equals(request.getSpecificity()))
				.map(BdmRdssDevice::getUniqueId)
				.collect(Collectors.toList());

		}

		boolean result = this.rdssDeviceMapper.deleteByIds(ids) > 0;
		if (result) {
			for (Long id : ids) {
				String key = BaseInfoConstants.RDSS_DEVICE_TYPE + "-" + id;
				redisTemplate.delete(BaseInfoConstants.BASEINFO_DEVICE + ":" + key);
			}
			this.iotCardService.deleteByDeviceIds(ids, DeviceTypeEnum.RDSS.getSymbol());
			if (!uniqueIdList.isEmpty()) {
				//跟新bdm_terminal
				terminalService.updateFormalByDeviceSeqs(uniqueIdList);
			}
			if (!deviceNumList.isEmpty()) {
				//更新bdm_device_code
				deviceCodeService.updateActivatedByDeviceNum(deviceNumList);
			}

			DeviceInfo deviceInfo = new DeviceInfo();
			deviceInfo.setOperation(Operation.DELETE);
			Long lastId = ids[ids.length - 1];
			deviceInfo.setDeviceId(lastId);
			deviceInfo.setDeviceIds(CollUtil.newHashSet(ids));
			try {
				this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_rdss_device", JSON.toJSONString(deviceInfo));
			} catch (Exception e) {
				log.error("短报文终端信息更新消息发送到kafka失败", e);
			}
			//messageClient
			try {
				List<Object> terminals = new ArrayList<>(list);
				messageClient.terminalBatch(CommonConstant.OPER_TYPE_DELETE, BaseInfoConstants.RDSS_DEVICE_TYPE, terminals);
			} catch (Exception e) {
				log.error("消息发送到messageClient失败：" + e.getMessage());
			}

			//同步更新抽象监控对象实体表
			bdmAbstractDeviceService.deleteByIds(ids);
			// 同步更新待分配终端对象实体表 删除
			if (!uniqueIdList.isEmpty()) {
				bdmVirtualTargetService.updateByUniqueId(uniqueIdList);

				QueryWrapper<BdmVirtualTarget> virtualTargetQueryWrapper = new QueryWrapper<>();
				if (!uniqueIdList.isEmpty()) {
					MybatisPlusWrapperUitls.longListIn(wrapper, "unique_id", uniqueIdList);
				}
				virtualTargetQueryWrapper.select("id");
				List<Long> idList = bdmVirtualTargetService.getBaseMapper()
					.selectList(virtualTargetQueryWrapper)
					.stream()
					.map(BdmVirtualTarget::getId)
					.collect(Collectors.toList());
				// 同步更新抽象监控对象实体表
				if (!idList.isEmpty()) {
					bdmAbstractTargetService.deleteByIds(idList.toArray(new Long[0]));
					for (Long id : idList) {
						String targetKey = BaseInfoConstants.VIRTUAL_TARGET_TYPE + "-" + id;
						redisTemplate.delete(BaseInfoConstants.BASEINFO_TARGET + ":" + targetKey);
					}
				}
			}
		}

		return result;
	}


	@Override
	public IPage<PersonNoBingResponse> select(DeviceNoBindRequest deviceNoBindRequest, String account, String deptIds) {
		Page page = new Page();
		page.setCurrent(deviceNoBindRequest.getCurrent());
		page.setSize(deviceNoBindRequest.getSize());
		return this.rdssDeviceMapper.select(page, deviceNoBindRequest, account, deptIds);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public List<RdssDeviceRequest> importExcel(List<RdssDeviceRequest> list, Long userId) {
		List<RdssDeviceRequest> duplicateRequests = getDuplicateRequests(list);
		// 假设requests和duplicateRequests已经被正确初始化
		List<RdssDeviceRequest> requests = new ArrayList<>(list);
		// 移除duplicateRequests中的所有元素
		requests.removeIf(request -> duplicateRequests.contains(request));
		if (!requests.isEmpty()) {
			List<RdssDeviceRequest> filteredList = processRequests(requests, userId, duplicateRequests);
			if (!filteredList.isEmpty()) {
				List<Long> arrayList = updateDatabaseAndCache(filteredList);
				sendNotifications(filteredList, arrayList);
			}
		}
		return duplicateRequests;
	}

	/**
	 * 重复数据
	 *
	 * @param list
	 * @return
	 */
	public List<RdssDeviceRequest> getDuplicateRequests(List<RdssDeviceRequest> list) {
		// 1. 统计每个number出现的次数
		List<String> numberList = list.stream()
			.map(RdssDeviceRequest::getNumbers)
			.filter(Objects::nonNull) // 确保numbers不为空
			.filter(number -> !number.isEmpty()) // 确保numbers不为空字符串
			.collect(Collectors.toList());

		// 2. 过滤出重复的number
		List<String> duplicates = numberList.stream()
			.filter(number -> Collections.frequency(numberList, number) > 1)
			.distinct()
			.collect(Collectors.toList());

		List<String> uniqueIdList = list.stream()
			.map(RdssDeviceRequest::getUniqueId)
			.collect(Collectors.toList());

		List<String> duplicateUniqueIds = uniqueIdList.stream()
			.filter(id -> Collections.frequency(uniqueIdList, id) > 1)
			.distinct()
			.collect(Collectors.toList());

		return list.stream()
			.filter(rdssDeviceRequest -> duplicates.contains(rdssDeviceRequest.getNumbers()) || duplicateUniqueIds.contains(rdssDeviceRequest.getUniqueId()))
			.peek(rdssDeviceRequest -> {
				if (duplicates.contains(rdssDeviceRequest.getNumbers())) {
					rdssDeviceRequest.setMsg("导入的数据物联网卡号重复");
				}
				if (duplicateUniqueIds.contains(rdssDeviceRequest.getUniqueId())) {
					rdssDeviceRequest.setMsg("导入的数据序列号重复");
				}
			})
			.collect(Collectors.toList());
	}

	/**
	 * 数据处理
	 */
	private List<RdssDeviceRequest> processRequests(List<RdssDeviceRequest> requests, Long userId, List<RdssDeviceRequest> duplicateRequests) {
		// 按部门ID分组
		Map<Long, List<RdssDeviceRequest>> groupedByDept = requests.stream()
			.collect(Collectors.groupingBy(RdssDeviceRequest::getDeptId));

		List<RdssDeviceRequest> filterList = new ArrayList<>();

		for (Map.Entry<Long, List<RdssDeviceRequest>> entry : groupedByDept.entrySet()) {
			// TODO 终端赋码号不存在！和 序列号已存在！可连表查进行判断 检查输入的赋码号的合法性
			Map<String, BdmDeviceLedger> deviceLedgerMap = getDeviceLedgers(entry.getKey())
				.stream()
				.collect(Collectors.toMap(BdmDeviceLedger::getDeviceNum, ledger -> ledger));

			List<RdssDeviceRequest> requestsForDept = entry.getValue();

			// 过滤出该部门下设备编号存在于deviceLedgers中的请求
			List<RdssDeviceRequest> validRequestsForDept = requestsForDept.stream()
				.filter(request -> {
					if (!BdmTypeEnum.NEW.getValue().equals(request.getSpecificity())) {
						return true;
					} else {
						return isValidRequest(request, deviceLedgerMap);
					}
				})
				.collect(Collectors.toList());

			filterList.addAll(validRequestsForDept);

			//检查输入的赋码号的合法性
			requestsForDept.stream()
				.filter(request -> BdmTypeEnum.NEW.getValue().equals(request.getSpecificity()) && !deviceLedgerMap.containsKey(request.getDeviceNum()))
				.peek(request -> request.setMsg("终端赋码号不存在！"))
				.forEach(duplicateRequests::add);
		}

		if (!filterList.isEmpty()) {

			List<String> uniqueIdList = filterList.stream()
				.filter(Objects::nonNull)
				.map(RdssDeviceRequest::getUniqueId)
				.collect(Collectors.toList());

			QueryWrapper<BdmRdssDevice> wrapper = new QueryWrapper<>();
			wrapper.eq("deleted", 0);
			if (!uniqueIdList.isEmpty()) {
				MybatisPlusWrapperUitls.longListIn(wrapper, "unique_id", uniqueIdList);
			}
			wrapper.select("unique_id");

			//序列号判重
			List<String> uniqueIds = baseMapper.selectList(wrapper)
				.stream()
				.map(BdmRdssDevice::getUniqueId)
				.collect(Collectors.toList());

			List<RdssDeviceRequest> resultList = filterList.stream()
				.filter(rdssDeviceRequest ->
					rdssDeviceRequest.getUniqueId() != null && !uniqueIds.contains(rdssDeviceRequest.getUniqueId()))
				.collect(Collectors.toList());

			filterList.stream()
				.filter(request -> !resultList.contains(request))
				.peek(request -> request.setMsg("序列号已存在！"))
				.forEach(duplicateRequests::add);

			filterList = resultList;

			if (!filterList.isEmpty()) {
				List<String> numberedList = resultList.stream()
					.filter(Objects::nonNull)
					.map(RdssDeviceRequest::getNumbers)
					.collect(Collectors.toList());

				List<String> numberList = new ArrayList<>();

				if (!numberedList.isEmpty()) {

					log.info("[importExcel]终端信息导入，将要判断是否是底座登录");
					DataAuthCE ceDataAuth = ceTokenUtil.getDataAuth();
					boolean isCE = ceTokenUtil.isCELogin();
					if(isCE){
						log.info("[importExcel]终端信息导入，国能底座登录");
						//如果是国能底座，则查询数据权限范围内的物联网卡信息
						String account = ceDataAuth.getAccount();
						String deptArrayStr = ceDataAuth.getOrgListStr();
						numberList = iotCardService.queryIotCE(numberedList, account, deptArrayStr);
						log.info("[importExcel]终端信息导入，获取数据权限范围内的物联网卡数量为：{}", numberList==null?0:numberList.size());
					}else{
						userId = AuthUtil.isAdministrator() ? null : userId;
						// 物联网卡验证
						numberList = iotCardService.queryIot(numberedList, userId);
					}
				}

				final List<String> finalNumberList = numberList;

				List<RdssDeviceRequest> filteredList = resultList.stream()
					.filter(rdssDeviceRequest ->
						rdssDeviceRequest.getNumbers() == null || finalNumberList.contains(rdssDeviceRequest.getNumbers()))
					.collect(Collectors.toList());

				// 将除了filteredList的数据加到duplicateRequests
				resultList.stream()
					.filter(request -> !filteredList.contains(request))
					.peek(request -> request.setMsg("该物联网卡号不存在或已绑定！"))
					.forEach(duplicateRequests::add);

				filterList = filteredList;
			}

		}

		return filterList;
	}

	/**
	 * 查询出库到本单位数据
	 */
	private List<BdmDeviceLedger> getDeviceLedgers(Long deptId) {
		return deviceLedgerService.getBaseMapper()
			.selectList(new QueryWrapper<BdmDeviceLedger>()
				.eq("device_type", BaseInfoConstants.RDSS_DEVICE_TYPE)
				.eq("user_dept_id", deptId));
	}

	/**
	 * 新设备给设置正确的赋码信息
	 */
	private boolean isValidRequest(RdssDeviceRequest request, Map<String, BdmDeviceLedger> targetMap) {
		BdmDeviceLedger deviceLedger = targetMap.get(request.getDeviceNum());
		if (deviceLedger != null) {
			request.setModel(deviceLedger.getModel());
			request.setImei(deviceLedger.getImei());
			request.setVendor(deviceLedger.getVendor());
			request.setUniqueId(deviceLedger.getUniqueId());
			request.setBdChipSn(deviceLedger.getBdChipSn());
			request.setDeviceType(deviceLedger.getDeviceType());
			request.setCategory(deviceLedger.getCategory());
			return true;
		}
		return false;
	}

	/**
	 * 更新数据库和进行数据redis缓存
	 */
	private List<Long> updateDatabaseAndCache(List<RdssDeviceRequest> filteredList) {
		SnowflakeIdWorker idWorker = new SnowflakeIdWorker(BaseInfoConstants.RDSS_DEVICE_TYPE, BaseInfoConstants.ZERO, BaseInfoConstants.ZERO);
		TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(BaseInfoConstants.VIRTUAL_TARGET_TYPE, BaseInfoConstants.ZERO, BaseInfoConstants.ZERO);
		Map<String, BdmRdssDevice> rdssMap = baseMapper.selectList(new QueryWrapper<BdmRdssDevice>().eq("deleted", 1))
			.stream()
			.collect(Collectors.toMap(BdmRdssDevice::getUniqueId, RdssDevice -> RdssDevice));
		//用于判断恢复被删除的设备时，判断是执行新增还是恢复操作
		Map<String, BdmVirtualTarget> allVirtualTargetMap = bdmVirtualTargetService.list()
			.stream()
			.collect(Collectors.toMap(BdmVirtualTarget::getNumber, VirtualTarget -> VirtualTarget,(existing, replacement) -> existing));
		Map<String, BdmAbstractTarget> allBatMap = bdmAbstractTargetService.list()
			.stream()
			.collect(Collectors.toMap(BdmAbstractTarget::getNumber, bat -> bat,(existing, replacement) -> existing));
		// 创建一个 Map 来存储所有的键值对
		List<TerminalIotRequest> terminalIotRequests = new ArrayList<>();
		Map<String, String> map = new HashMap<>();
		List<Long> arrayList = new ArrayList<>();
		Map<String, String> targrtMap = new HashMap<>();
		List<BdmVirtualTarget> virtualTargets = new ArrayList<>();
		List<BdmAbstractDevice> abstractDevices = new ArrayList<>();
		List<BdmAbstractTarget> abstractTargets = new ArrayList<>();

		// 在处理filteredList之前，收集所有新设备的序列号
		Set<String> newDeviceUniqueIds = filteredList.stream()
			.filter(request -> BdmTypeEnum.NEW.getValue().equals(request.getSpecificity()))
			.map(RdssDeviceRequest::getUniqueId)
			.collect(Collectors.toSet());

		Map<String, BdmVirtualTarget> virtualTargetMap = new HashMap<>();
		// 一次性查询所有匹配的BdmVirtualTarget记录
		if (!newDeviceUniqueIds.isEmpty()) {
			List<BdmVirtualTarget> allVirtualTargets = bdmVirtualTargetService.list(
				new QueryWrapper<BdmVirtualTarget>().in("number", newDeviceUniqueIds).select("id").select("number")
			);
			// 将查询结果存储在一个Map中，以便后续快速查找
			virtualTargetMap = allVirtualTargets.stream().collect(Collectors.toMap(BdmVirtualTarget::getNumber, Function.identity()));
		}
		for (RdssDeviceRequest request : filteredList) {
			BdmRdssDevice device = rdssMap.get(request.getUniqueId());
			// device为空新增，不为空修改
			long deviceId = (device == null) ? idWorker.nextId() : device.getId();
			request.setId(deviceId);
			request.setDeleted(0);
			//设备赋码
			if (device == null) {
				if (request.getSpecificity().equals(BdmTypeEnum.OLD.getValue())) {
					R<String> r1 = deviceNumUtils.deviceNumProducer(ProductTypeEnum.O.name());
					if (r1.isSuccess()) {
						request.setDeviceNum(r1.getData());
					}
				}
				if (BdmTypeEnum.SPECIFICITY.getValue().equals(request.getSpecificity())) {
					R<String> r1 = deviceNumUtils.deviceNumProducer(ProductTypeEnum.S.name());
					if (r1.isSuccess()) {
						request.setDeviceNum(r1.getData());
					}
				}
			} else {
				request.setDeviceNum(device.getDeviceNum());
			}
			//新设备的定位模式单独处理
			if (BdmTypeEnum.NEW.getValue().equals(request.getSpecificity())) {
				request.setGnssMode(GnssModeEnum.SINGLEBEIDOU.getValue());
			}
			request.setCreateTime(new Date());
			BdmRdssDevice bdmRdssDevice = this.init(request);
			BdmAbstractTarget bdmAbstractTarget = new BdmAbstractTarget();
			if (device == null) {
				//同步到待分配终端对象实体表
				BdmVirtualTarget virtualTarget = new BdmVirtualTarget();
				BeanUtils.copyProperties(bdmRdssDevice, virtualTarget, "id");
				virtualTarget.setId(targetId.nextId());
				virtualTarget.setTargetType(0);
				virtualTarget.setDeviceCode(bdmRdssDevice.getDeviceNum());
				virtualTarget.setNumber(bdmRdssDevice.getUniqueId());
				virtualTargets.add(virtualTarget);
				// TODO 同步到抽象监控对象实体表 abstractTargets
				bdmAbstractTarget.setId(virtualTarget.getId());
				bdmAbstractTarget.setNumber(bdmRdssDevice.getUniqueId());
				bdmAbstractTarget.setName(bdmRdssDevice.getUniqueId());
				bdmAbstractTarget.setTargetType(0);
				bdmAbstractTarget.setDeptId(bdmRdssDevice.getDeptId());
				bdmAbstractTarget.setCategory(0);
				bdmAbstractTarget.setDeleted(0);
				abstractTargets.add(bdmAbstractTarget);
				bdmRdssDevice.setTargetId(virtualTarget.getId());
				this.rdssDeviceMapper.insertRdss(bdmRdssDevice);
				//同步到抽象监控对象实体表
				BdmAbstractDevice abstractDevice = new BdmAbstractDevice();
				BeanUtils.copyProperties(bdmRdssDevice, abstractDevice);
				abstractDevice.setTargetType(0);
				abstractDevice.setDeleted(0);
				abstractDevices.add(abstractDevice);
			} else {
				//同步到待分配终端对象实体表
				BdmVirtualTarget virtualTarget = new BdmVirtualTarget();
				BeanUtils.copyProperties(bdmRdssDevice, virtualTarget, "id");
				virtualTarget.setTargetType(0);
				virtualTarget.setDeviceCode(bdmRdssDevice.getDeviceNum());
				virtualTarget.setNumber(bdmRdssDevice.getUniqueId());
				if (allVirtualTargetMap.containsKey(device.getUniqueId())) {
					virtualTarget.setId(virtualTargetMap.get(bdmRdssDevice.getUniqueId()).getId());
					bdmVirtualTargetService.updateById(virtualTarget);
				} else {
					virtualTarget.setId(targetId.nextId());
					virtualTargets.add(virtualTarget);
				}
				// TODO 更新到抽象监控对象实体表
				bdmAbstractTarget.setNumber(bdmRdssDevice.getUniqueId());
				bdmAbstractTarget.setName(bdmRdssDevice.getUniqueId());
				bdmAbstractTarget.setTargetType(0);
				bdmAbstractTarget.setDeptId(bdmRdssDevice.getDeptId());
				bdmAbstractTarget.setCategory(0);
				bdmAbstractTarget.setDeleted(0);
				bdmAbstractTarget.setId(virtualTarget.getId());
				if (allBatMap.containsKey(device.getUniqueId())) {
					bdmAbstractTargetService.updateById(bdmAbstractTarget);
				} else {
					abstractTargets.add(bdmAbstractTarget);
				}

				bdmRdssDevice.setTargetId(virtualTarget.getId());
				baseMapper.updateById(bdmRdssDevice);
				//同步到抽象监控对象实体表
				BdmAbstractDevice abstractDevice = new BdmAbstractDevice();
				BeanUtils.copyProperties(bdmRdssDevice, abstractDevice);
				abstractDevice.setTargetType(0);
				abstractDevice.setDeleted(0);
				bdmAbstractDeviceService.updateById(abstractDevice);
			}
			request.setId(bdmRdssDevice.getId());
			arrayList.add(bdmRdssDevice.getId());
			if (bdmRdssDevice.getNumbers() != null && !bdmRdssDevice.getNumbers().isEmpty()) {
				TerminalIotRequest terminalIotRequest = new TerminalIotRequest();
				terminalIotRequest.setId(bdmRdssDevice.getId());
				terminalIotRequest.setNumber(request.getNumbers());
				terminalIotRequest.setDeviceType(BaseInfoConstants.RDSS_DEVICE_TYPE);
				terminalIotRequests.add(terminalIotRequest);
			}

			String key = bdmRdssDevice.getDeviceType() + "-" + bdmRdssDevice.getId();
			Map<String, Object> innerMap = new HashMap<>();
			innerMap.put("uniqueId", bdmRdssDevice.getUniqueId());
			innerMap.put("deviceNum", bdmRdssDevice.getDeviceNum());
			innerMap.put("category", bdmRdssDevice.getCategory());
			innerMap.put("deviceType", bdmRdssDevice.getDeviceType());
			innerMap.put("deptId", bdmRdssDevice.getDeptId());
			innerMap.put("iotProtocol", bdmRdssDevice.getIotProtocol());
			try {
				map.put(key, new ObjectMapper().writeValueAsString(innerMap));
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			// 待分配终端对象 缓存到 baseinfo_target
			if (bdmRdssDevice.getSpecificity().equals(BdmTypeEnum.NEW.getValue())) {
				String targetKey = TargetTypeEnum.ENDPOINTS.getSymbol() + "-" + bdmAbstractTarget.getId();
				Map<String, Object> targetInnerMap = new HashMap<>();
				targetInnerMap.put("targetName", bdmAbstractTarget.getNumber());
				targetInnerMap.put("targetType", bdmAbstractTarget.getTargetType());
				targetInnerMap.put("deptId", bdmAbstractTarget.getDeptId());
				try {
					targrtMap.put(targetKey, new ObjectMapper().writeValueAsString(targetInnerMap));
				} catch (JsonProcessingException e) {
					e.printStackTrace();
				}
			}
		}
		if (!virtualTargets.isEmpty()) {
			MapperUtils.splitListByCapacity(virtualTargets, MapperUtils.DEFAULT_CAPACITY)
				.forEach(virtualTargetList -> this.bdmVirtualTargetService.insertBatch(virtualTargetList));
		}
		if (!abstractDevices.isEmpty()) {
			MapperUtils.splitListByCapacity(abstractDevices, MapperUtils.DEFAULT_CAPACITY)
				.forEach(abstractDeviceList -> this.bdmAbstractDeviceService.insertBatch(abstractDeviceList));
		}
		// 新设备同步到抽象监控对象实体表
		if (!abstractTargets.isEmpty()) {
			MapperUtils.splitListByCapacity(abstractTargets, MapperUtils.DEFAULT_CAPACITY)
				.forEach(abstractTargetList -> this.bdmAbstractTargetService.insertBatch(abstractTargetList));
		}
		if (!map.isEmpty()) {
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);
		}
		if (!targrtMap.isEmpty()) {
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, targrtMap);
		}
		if (!terminalIotRequests.isEmpty()) {
			this.iotCardService.updateDeviceId(terminalIotRequests);
		}
		// 查询 deviceNum 列表
		List<String> deviceNumList = filteredList.stream()
			.filter(request -> BdmTypeEnum.NEW.getValue().equals(request.getSpecificity()))
			.map(RdssDeviceRequest::getDeviceNum)
			.collect(Collectors.toList());
		// 查询 uniqueId 列表
		List<String> uniqueIdList = filteredList.stream()
			.filter(request -> BdmTypeEnum.NEW.getValue().equals(request.getSpecificity()))
			.map(RdssDeviceRequest::getUniqueId)
			.collect(Collectors.toList());
		if (!uniqueIdList.isEmpty()) {
			//跟新bdm_terminal
			terminalService.updateFormalByDeviceSeqs(uniqueIdList);
		}
		if (!deviceNumList.isEmpty()) {
			//更新bdm_device_code
			deviceCodeService.updateActivatedByDeviceNum(deviceNumList);
		}
		return arrayList;
	}

	/**
	 * 发送消息
	 */
	private void sendNotifications(List<RdssDeviceRequest> filteredList, List<Long> arrayList) {
		RdssDeviceRequest request = filteredList.get(filteredList.size() - 1);
		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.IMPORT);
		deviceInfo.setDeviceId(request.getId());
		Set<Long> ids = filteredList.stream().map(RdssDeviceRequest::getId).collect(Collectors.toSet());
		deviceInfo.setDeviceIds(ids);
		deviceInfo.setDeviceType(request.getDeviceType());
		deviceInfo.setDeviceModel(request.getModel());
		deviceInfo.setDeviceNum(request.getDeviceNum());
		deviceInfo.setDeviceUniqueId(request.getUniqueId());
		deviceInfo.setDeptId(request.getDeptId());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_rdss_device", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("短报文终端信息更新消息发送到kafka失败", e);
		}
		QueryWrapper<BdmRdssDevice> wrapper = new QueryWrapper<>();
		if (!arrayList.isEmpty()) {
			MybatisPlusWrapperUitls.longListIn(wrapper, "id", arrayList);
			List<BdmRdssDevice> rdssDevices = baseMapper.selectList(wrapper);
			//messageClient
			List<Object> terminals = new ArrayList<>(rdssDevices);
			try {
				messageClient.terminalBatch(CommonConstant.OPER_TYPE_ADD, BaseInfoConstants.RDSS_DEVICE_TYPE, terminals);
			} catch (Exception e) {
				log.error("消息发送到messageClient失败：" + e.getMessage());
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateBatchByWorkerId(Long id, Integer targetType) {
		this.rdssDeviceMapper.updateBatchByWorkerId(id, targetType);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateBatch(List<PersonTerminalRequest> requests, Long id, Integer targetType, String targetName, Long deptId) {
		List<Long> ids = requests.stream().map(PersonTerminalRequest::getId).collect(Collectors.toList());
		if (!ids.isEmpty()) {
			this.rdssDeviceMapper.updateBatch(ids, id, targetType, targetName, deptId);
		}

		Map<String, String> map = new HashMap<>();
		for (PersonTerminalRequest request : requests) {
			String key = request.getDeviceType() + "-" + request.getId();
			Map<String, Object> innerMap = new HashMap<>();
			innerMap.put("uniqueId", request.getUniqueId());
			innerMap.put("deviceNum", request.getDeviceNum());
			innerMap.put("category", request.getCategory());
			innerMap.put("deviceType", request.getDeviceType());
			innerMap.put("deptId", request.getDeptId());
			innerMap.put("iotProtocol", request.getIotProtocol());
			try {
				map.put(key, new ObjectMapper().writeValueAsString(innerMap));
			} catch (JsonProcessingException e) {
				log.error(e.getMessage());
			}
		}
		if (!map.isEmpty()) {
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);
		}
	}

	@Override
	public List<PersonNoBingResponse> selectBindByWorkId(Long id, Integer targetType) {
		return this.rdssDeviceMapper.selectBindByWorkId(id, targetType);
	}

	@Override
	public List<WorkerBingResponse> selectByWorkId(Long id, Integer targetType) {
		return this.rdssDeviceMapper.selectByWorkId(id, targetType);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void deleteByTargetIds(Long[] ids, Integer targetType) {
		this.rdssDeviceMapper.deleteByTargetIds(ids, targetType);
	}

	@Override
	public long countByUserRole(DataAuthCE ceDataAuth) {
		AuthInfo response = deptProcessingUtil.handle(ceDataAuth);
		return baseMapper.countByUserRole(response.getAccount(), response.getOrgList());
	}

	@Override
	public void updateDept(Long id, Integer targetType, Long deptId) {
		List<BdmRdssDevice> rdssDevices = baseMapper.selectList(
			new QueryWrapper<BdmRdssDevice>()
				.eq("target_id", id)
				.eq("target_type", targetType)
				.eq("deleted", 0)
				.select("id,unique_id,device_type,dept_id,category,device_num,iot_protocol")
		);
		if (!rdssDevices.isEmpty()) {
			baseMapper.updateDept(id, targetType, deptId);

			Map<String, String> map = new HashMap<>();
			for (BdmRdssDevice rdssDevice : rdssDevices) {
				String key = rdssDevice.getDeviceType() + "-" + rdssDevice.getId();
				Map<String, Object> innerMap = new HashMap<>();
				innerMap.put("uniqueId", rdssDevice.getUniqueId());
				innerMap.put("deviceNum", rdssDevice.getDeviceNum());
				innerMap.put("category", rdssDevice.getCategory());
				innerMap.put("deviceType", rdssDevice.getDeviceType());
				innerMap.put("deptId", deptId);
				innerMap.put("iotProtocol", rdssDevice.getIotProtocol());
				try {
					map.put(key, new ObjectMapper().writeValueAsString(innerMap));
				} catch (JsonProcessingException e) {
					e.printStackTrace();
				}
			}
			if (!map.isEmpty()) {
				redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);
			}
		}
	}

	/**
	 * 对象字段初始化
	 *
	 * @param request
	 * @return
	 */
	public BdmRdssDevice init(RdssDeviceRequest request) {
		BdmRdssDevice rdssDevice = new BdmRdssDevice();
		rdssDevice.setId(request.getId());
		rdssDevice.setUniqueId(request.getUniqueId() != null ? request.getUniqueId() : "");
		rdssDevice.setImei(request.getImei() != null ? request.getImei() : "");
		rdssDevice.setModel(request.getModel() != null ? request.getModel() : "");
		rdssDevice.setVendor(request.getVendor() != null ? request.getVendor() : "");
		rdssDevice.setBdChipSn(request.getBdChipSn() != null ? request.getBdChipSn() : "");
		rdssDevice.setDeviceType(DeviceTypeEnum.RDSS.getSymbol());
		rdssDevice.setSpecificity(request.getSpecificity() != null ? request.getSpecificity() : 0);
		rdssDevice.setDeptId(request.getDeptId() != null ? request.getDeptId() : 0);
		rdssDevice.setCategory(request.getCategory() != null ? request.getCategory() : 0);
		rdssDevice.setDeviceNum(request.getDeviceNum() != null ? request.getDeviceNum() : "");
		rdssDevice.setBdCard(request.getBdCard() != null ? request.getBdCard() : "");
		rdssDevice.setBdCardLevel(request.getBdCardLevel() != null ? request.getBdCardLevel() : 0);
		rdssDevice.setInstalldate(request.getInstalldate() != null ? request.getInstalldate() : null);
		rdssDevice.setCreateTime(request.getCreateTime() != null ? request.getCreateTime() : null);
		rdssDevice.setUpdateTime(request.getUpdateTime() != null ? request.getUpdateTime() : new Date());
		rdssDevice.setDeleted(request.getDeleted() != null ? request.getDeleted() : 0);
		rdssDevice.setScenario(request.getScenario() != null ? request.getScenario() : 0);
		rdssDevice.setDomain(request.getDomain() != null ? request.getDomain() : 0);
		rdssDevice.setGnssMode(request.getGnssMode() != null ? request.getGnssMode() : 0);
		rdssDevice.setIotProtocol(request.getIotProtocol() != null ? request.getIotProtocol() : IotProtocolEnum.JT808.getCode());
		rdssDevice.setTerminalId(request.getTerminalId() != null ? request.getTerminalId() : "");
		rdssDevice.setAssetType(request.getAssetType() != null ? request.getAssetType() : 0);
		rdssDevice.setOwnDeptType(request.getOwnDeptType() != null ? request.getOwnDeptType() : 0);
		rdssDevice.setOwnDeptName(request.getOwnDeptName() != null ? request.getOwnDeptName() : "");
		return rdssDevice;
	}

	@Override
	public int bindTarget(Long deviceId, Long targetId, Integer targetType,String targetName){
		return this.rdssDeviceMapper.bindTarget(deviceId,targetId, targetType,targetName);
	}
}
